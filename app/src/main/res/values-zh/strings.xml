<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="app_name">123RFID 移动端</string>
    <string name="hello_world">你好世界！</string>
    <string name="action_settings">RFID 设置</string>
    <string name="rapid_read">快速读取</string>
    <string name="title_activity_inventory">库存活动</string>

    <!-- Options available in the Navigation Drawer of the application -->
    <string-array name="options_array">
        <item>首页</item>
        <item>快速读取</item>
        <item>库存</item>
        <item>定位标签</item>
        <item>设置</item>
        <item>标签写入</item>
        <item>预过滤器</item>
        <item>读取器列表</item>
        <item>关于</item>
        <item>配置文件</item>
    </string-array>

    <string name="navigation_drawer_open">打开导航抽屉</string>
    <string name="navigation_drawer_close">关闭导航抽屉</string>
    <string name="nav_header_title">即时了解您的运营状况</string>
    <string name="nav_header_desc">123RFID 移动端</string>

    <!-- Strings for the navigation drawer -->
    <string name="drawer_open">打开导航抽屉</string>
    <string name="drawer_close">关闭导航抽屉</string>
    <string name="action_websearch">网络搜索</string>
    <string name="app_not_available">抱歉，没有可用的网络浏览器</string>
    <string name="hello_blank_fragment">空白片段</string>

    <!-- Strings for actions in Inventory Fragment -->
    <string name="action_none">无</string>
    <string name="action_user">用户</string>
    <string name="action_reserved">保留</string>
    <string name="action_tid">TID</string>
    <string name="action_epic">EPC</string>
    <string name="title_activity_antenna_settings">天线</string>
    <string name="title_activity_readers_list">读取器列表</string>
    <string name="title_activity_application_settings">应用程序</string>
    <string name="title_activity_singulation_control">分离控制</string>
    <string name="unique_tag_settings">唯一标签设置</string>
    <string name="unique_tag_reads">报告唯一标签</string>
    <string name="report_unique_tags">报告唯一标签</string>

    <!-- Items for link profile -->
    <string-array name="link_profile_array">
        <item>RF 模式 1</item>
        <item>RF 模式 2</item>
        <item>RR 模式 1</item>
        <item>RR 模式 2</item>
        <item>RS 模式 1</item>
        <item>RS 模式 2</item>
    </string-array>

    <!-- Strings for Singulation Control Fragment -->
    <!-- Items for sessions -->
    <string-array name="session_array">
        <item>S0</item>
        <item>S1</item>
        <item>S2</item>
        <item>S3</item>
    </string-array>

    <!-- Items for populating tags -->
    <string-array name="tag_population_array">
        <item>30</item>
        <item>100</item>
        <item>200</item>
        <item>300</item>
        <item>400</item>
        <item>500</item>
        <item>600</item>
    </string-array>

    <!-- Items for inventory state -->
    <string-array name="inventory_state_array">
        <item>状态 A</item>
        <item>状态 B</item>
        <item>AB 翻转</item>
    </string-array>

    <!-- Items for sl flag -->
    <string-array name="sl_flags_array">
        <item>全部</item>
        <item>未断言</item>
        <item>已断言</item>
    </string-array>

    <string-array name="inv_menu_items">
        <item>无</item>
        <item>用户</item>
        <item>保留</item>
        <item>TID</item>
        <item>EPC</item>
        <item>篡改</item>
    </string-array>

    <!-- Strings for Access Operations Read Write Fragment -->
    <!-- Items for memory bank state -->
    <string-array name="acess_read_write_memory_bank_array">
        <item>EPC</item>
        <item>TID</item>
        <item>用户</item>
        <item>访问密码</item>
        <item>销毁密码</item>
    </string-array>

    <!-- Strings for Access Operations Lock Fragment -->
    <!-- Items for memory bank state -->
    <string-array name="acess_lock_memory_bank_array">
        <item>EPC</item>
        <item>访问密码</item>
        <item>销毁密码</item>
        <item>TID</item>
        <item>用户</item>
        <item>全部</item>
    </string-array>

    <!-- Items for lock privilege state -->
    <string-array name="acess_lock_privilege_array">
        <item>读写</item>
        <item>永久锁定</item>
        <item>永久解锁</item>
        <item>解锁</item>
    </string-array>

    <!-- Strings for Access Operations Kill Fragment -->
    <!-- Items for tagID -->
    <string-array name="pre_filter_memory_bank_array">
        <item>EPC</item>
        <item>TID</item>
        <item>用户</item>
    </string-array>

    <string-array name="pre_filter_action_array">
        <item>库存 A 非库存 B 或 断言 SL 非 未断言 SL</item>
        <item>库存 A 或 断言 SL</item>
        <item>非库存 B 或 非 未断言 SL</item>
        <item>库存 A2BB2A 非库存 A 或 负 SL 非 断言 SL</item>
        <item>库存 B 非库存 A 或 未断言 SL 非 断言 SL</item>
        <item>库存 B 或 未断言 SL</item>
        <item>非库存 A 或 非 断言 SL</item>
        <item>非库存 A2BB2A 或 非 负 SL</item>
    </string-array>

    <string-array name="pre_filter_target_options">
        <item>会话 S0</item>
        <item>会话 S1</item>
        <item>会话 S2</item>
        <item>会话 S3</item>
        <item>SL 标志</item>
    </string-array>

    <string-array name="pre_filter_tag_id_array">
        <item>AD9915404190725965400412</item>
        <item>AD9915404190725965400413</item>
        <item>AD9915404190725965400414</item>
        <item>AD9915404190725965400415</item>
        <item>AD9915404190725965400416</item>
    </string-array>

    <string name="title_activity_navigation_drawer">导航抽屉活动</string>
    <string name="title_activity_access_operations">标签写入活动</string>

    <!-- Strings for StartStopTrigger Fragment -->
    <!-- Strings for start trigger Selection -->
    <string-array name="start_trigger_array">
        <item>立即</item>
        <item>手持</item>
        <item>周期性</item>
    </string-array>
    <!-- Strings for stop trigger Selection -->
    <string-array name="stop_trigger_array">
        <item>立即</item>
        <item>手持</item>
        <item>持续时间</item>
        <item>标签观察</item>
        <item>N 次尝试</item>
    </string-array>

    <!-- beeper volume -->
    <string-array name="beeper_volume_array">
        <item>高</item>
        <item>中</item>
        <item>低</item>
    </string-array>

    <!-- Strings for PIE Antenna Control Fragment -->
    <!-- Items for PIE -->
    <string-array name="pie_array">
        <item>1500</item>
        <item>2000</item>
    </string-array>

    <string-array name="pie0_array">
        <item>0</item>
    </string-array>

    <string-array name="pie_array_668">
        <item>668</item>
    </string-array>

    <string-array name="pie_array_2000">
        <item>2000</item>
    </string-array>

    <!-- Strings for PIE Antenna Control Fragment -->
    <!-- Items for TARI -->
    <string-array name="tari_array">
        <item>6250</item>
        <item>12500</item>
        <item>14600</item>
        <item>16700</item>
        <item>18800</item>
        <item>20900</item>
        <item>23000</item>
        <item>25000</item>
    </string-array>
    <string-array name="tari_array_625">
        <item>6250</item>
    </string-array>
    <string-array name="tari_array_668">
        <item>668</item>
    </string-array>
    <string-array name="tari0_array">
        <item>0</item>
    </string-array>
    <string-array name="tari_array_12_25">
        <item>12500</item>
        <item>14600</item>
        <item>16700</item>
        <item>18800</item>
        <item>20900</item>
        <item>23000</item>
        <item>25000</item>
    </string-array>
    <string-array name="tari_array_18_25">
        <item>18800</item>
        <item>20900</item>
        <item>23000</item>
        <item>25000</item>
    </string-array>
    <string-array name="tari_array_18">
        <item>12500</item>
        <item>14600</item>
        <item>16700</item>
        <item>18800</item>
    </string-array>
    <string-array name="tari_array_18_only">
        <item>18800</item>
    </string-array>
    <string-array name="tari_array_25_only">
        <item>25000</item>
    </string-array>
    <string-array name="tari_array_25_6300">
        <item>12500</item>
        <item>18800</item>
        <item>25000</item>
    </string-array>
    <string-array name="tari_array_18_6300">
        <item>12500</item>
        <item>18800</item>
    </string-array>

    <!-- Error Indicators -->
    <string name="none_paired">未找到设备</string>
    <string name="select_device">选择设备</string>
    <string name="none_found">未找到设备</string>
    <string name="error_security">SerialPortActivity 中的安全错误</string>
    <string name="error_unknown">串口活动中的未知错误</string>
    <string name="error_configuration">SerialPortActivity 中的配置错误</string>
    <string name="title_activity_base_receiver">BaseReceiverActivity</string>
    <string name="error_unsatisfied_link">SerialPortActivity 中的 UnsatisfiedLinkError</string>
    <string name="title_activity_main_activity2">MainActivity2</string>
    <string name="title_activity_regulatory_settings">监管设置</string>
    <string name="title_activity_start_stop_trigger">开始\\停止触发器</string>
    <string name="title_activity_save_configuration">保存配置</string>
    <string name="title_activity_about">关于</string>
    <string name="title_activity_beeper">蜂鸣器</string>
    <string name="led_settings_title">LED 设置</string>
    <string name="action_refresh">刷新</string>
    <string name="title_activity_settings_detail">设置详情</string>
    <string name="action_search">搜索</string>
    <string name="action_rr">快速读取</string>
    <string name="search_hint">输入标签 ID</string>

    <!-- Titles in ReadersList -->
    <string name="active_reader_title">活动读取器</string>
    <string name="available_readers_title">可用读取器</string>
    <string name="pair_new_reader">配对新设备</string>
    <!-- Titles in regulatory settings -->
    <string name="channel_selection">通道选择</string>

    <!-- Titles in connection settings -->
    <string name="reader_conn_sett">读取器连接设置</string>
    <string name="notification_settings">通知设置</string>
    <string name="title_activity_search_results">SearchResultsActivity</string>
    <string name="tag_reporting">标签报告</string>
    <string name="battery">电池</string>
    <string name="rr_unique_tags_title">唯一标签</string>
    <string name="rr_total_tag_title">总读取次数</string>
    <string name="rr_unique_tags_title_MM">缺失标签</string>
    <string name="rr_total_tag_title_MM">匹配标签</string>
    <string name="rr_read_time_title">读取时间</string>
    <string name="rr_read_rate_title">读取速率</string>
    <string name="resultTagContent">RFID 结果通知</string>
    <string name="scan_input_hint">扫描输入</string>
    <string name="scan_results">扫描结果</string>
    <string name="start_title">开始</string>
    <string name="tag_id">标签 ID</string>
    <string name="locationing_distance_title">相对距离</string>
    <string name="stop_title">停止</string>
    <string name="tag_pattern_title">标签模式</string>
    <string name="password_title">密码</string>
    <string name="memory_bank_title">存储库</string>
    <string name="offset_title">偏移（字）</string>
    <string name="length_title">长度（字）</string>
    <string name="data_title">数据</string>
    <string name="operation_success">操作成功完成</string>
    <string name="read_title">读取</string>
    <string name="write_title">写入</string>
    <string name="lock_privilege_title">锁定权限</string>
    <string name="lock_title">锁定</string>
    <string name="kill_pass_title">销毁密码</string>
    <string name="kill_title">销毁</string>
    <string name="offset_bits_title">偏移（字）</string>
    <string name="target_title">目标</string>
    <string name="action_title">操作</string>
    <string name="length_bits_title">长度（位）</string>
    <string name="enable_filter_1">启用过滤器 1</string>
    <string name="enable_filter_2">启用过滤器 2</string>
    <string name="power_level_title">功率级别 \n（十分之一分贝毫瓦）</string>
    <string name="link_profile_title">链路配置文件</string>
    <string name="status_success_message">操作成功</string>
    <string name="auto_detect_readers">自动检测读取器</string>
    <string name="auto_reconnect_reader">自动连接读取器</string>
    <string name="readers_available">读取器可用</string>
    <string name="readers_connection">读取器连接</string>
    <string name="reader_battery_status">读取器电池状态</string>
    <string name="region_title">区域</string>
    <string name="keymap_title">键映射</string>
    <string name="warning_title">警告：</string>
    <string name="country_selection_warning">警告：请仅选择您使用读取器的国家</string>
    <string name="first_seen_time">首次看到时间</string>
    <string name="last_seen_time">最后看到时间</string>
    <string name="pc">PC</string>
    <string name="rssi">RSSI</string>
    <string name="phase">相位</string>
    <string name="channel_index">通道索引</string>
    <string name="tag_seen_count">标签看到次数</string>
    <string name="save_to_sled">保存</string>
    <string name="battery_discharging_message">状态：放电中。</string>
    <string name="RFID_demo_app">123RFID 移动应用程序</string>
    <string name="moto_solutions">Zebra Technologies</string>
    <string name="app_version_title">应用程序版本</string>
    <string name="rfid_sled">RFID 读取器：</string>
    <string name="module_version">模块版本</string>
    <string name="radio_version">无线电版本</string>
    <string name="copyright_info">版权所有 © 2020</string>
    <string name="sl_flag_title">SL 标志</string>
    <string name="inv_state_title">库存状态</string>
    <string name="tag_population_title">标签数量</string>
    <string name="session_title">会话</string>
    <string name="sled_beeper_title">设备蜂鸣器</string>
    <string name="host_beeper_title">主机蜂鸣器</string>
    <string name="led_subtitle">主机 LED 启用</string>
    <string name="sled_led_subtitle">设备 LED 启用</string>
    <string name="beeper_volume_control_title">RFID 蜂鸣器音量控制</string>
    <string name="start_trigger_title">开始触发器</string>
    <string name="start_time_title">开始时间</string>
    <string name="periodic_title">周期（毫秒）</string>
    <string name="stop_trigger_title">停止触发器</string>
    <string name="duration_title">持续时间（毫秒）</string>
    <string name="report_title">报告</string>
    <string name="periodic_report_title">定期报告</string>
    <string name="paired_devices_title">配对设备</string>
    <string name="OK">确定</string>
    <string name="drawer_item_text">抽屉项目</string>
    <string name="drawer_image_desc">导航抽屉项目的图标</string>
    <string name="app_version">********</string>
    <string name="module_version_no">1.0.1.20</string>
    <string name="radio_version_no">2.4.3.55</string>
    <string name="battery_level_desc">显示读取器中电池电量的图标</string>
    <string name="battery_percentage_text">0%</string>
    <string name="inv_filter_title">存储库</string>
    <string name="inv_read_time_title">读取时间</string>
    <string name="inv_unique_title">唯一标签</string>
    <string name="inv_count_title">总读取次数</string>
    <string name="save_antenna_power">天线功率</string>
    <string name="save_link_profile">链路配置文件</string>
    <string name="save_seesion">会话</string>
    <string name="save_start_trigger">开始触发器</string>
    <string name="save_stop_trigger">停止触发器</string>
    <string name="save_beeper">蜂鸣器音量</string>
    <string name="save_region">区域</string>
    <string name="start">开始</string>
    <string name="stop">停止</string>
    <string name="start_trigger_released_title">触发器释放</string>
    <string name="home_rapid_read">快速读取</string>
    <string name="home_inventory">库存</string>
    <string name="err_locationing_failed">定位失败</string>
    <string name="err_stop_locationing_failed">停止定位失败</string>
    <string name="home_settings">设置</string>
    <string name="home_locate">定位标签</string>
    <string name="home_filter">预过滤器</string>
    <string name="tag_write">标签写入</string>
    <string name="inv_user_memory_title">内存 无</string>
    <string name="inv_pc_title">PC</string>
    <string name="inv_rssi_title">RSSI</string>
    <string name="inv_phase_title">相位</string>
    <string name="inv_channel_title">通道</string>
    <string name="setting_icon_desc">设置图标</string>
    <string name="stop_trigger_timeout">超时（毫秒）</string>
    <string name="stop_trigger_released">触发器释放</string>
    <string name="stop_trigger_pressed">触发器按下</string>
    <string name="stop_trigger_tag_observation">标签观察</string>
    <string name="stop_trigger_attempts">尝试次数</string>
    <string name="start_trigger_pressed">触发器按下</string>
    <string name="inventory_settings_title">库存模式</string>
    <string name="title_activity_inventory_settings">库存设置</string>
    <string name="title_pre_filters">预过滤器</string>
    <string name="wifi_settings_title">WiFi 设置</string>
    <string name="wifi_subtitle">启用 WiFi</string>
    <string name="wifi_settings">正在保存 WiFi 设置..</string>
    <string name="ct_settings">正在保存充电终端设置..</string>
    <string name="ct_settings_title">充电终端设置</string>
    <string name="ct_subtitle">启用充电终端</string>
    <string name="usb_mifi_settings_title">USB MiFi 设置</string>
    <string name="usb_mifi_subtitle">启用 USB MiFi</string>
    <string name="usb_mifi_settings">正在保存 USB MiFi 设置..</string>
    <string-array name="inventory_modes_array">
        <item>常规库存</item>
        <item>无线电引擎</item>
        <item>HAL</item>
    </string-array>
    <string name="save_antenna_title">天线</string>
    <string name="save_singulation_title">分离</string>
    <string name="save_tag_report_title">标签报告</string>
    <string name="save_start_stop_title">开始 \\ 停止触发器</string>
    <string name="save_beeper_title">蜂鸣器</string>
    <string name="save_regulatory_title">监管</string>
    <string name="save_keymapping_title">触发器键映射</string>
    <string name="save_config_batch_mode_title">批处理模式</string>
    <string name="save_dpo_title">电源管理</string>
    <string name="save_sled_beeper_volume_title">蜂鸣器音量</string>
    <string name="off">关闭</string>
    <string name="on">开启</string>
    <string name="error_disconnected">读取器未连接</string>
    <string name="error_reader_not_updated">读取器功能未更新</string>
    <string name="beeper_id_title">蜂鸣器 ID</string>
    <string name="beeper_tone_title">音调</string>
    <string name="beeper_volume_title">音量</string>
    <string name="beeper_duration_title">持续时间</string>
    <string name="title_model">型号</string>
    <string name="title_serial">序列号</string>
    <string name="colon">：</string>
    <string name="msg_read_succeed">读取成功</string>
    <string name="msg_write_succeed">写入成功</string>
    <string name="msg_lock_succeed">锁定成功</string>
    <string name="msg_kill_succeed">销毁成功</string>
    <string name="err_access_op_failed">标签写入操作失败</string>
    <string name="password_connection_title">需要连接密码！</string>
    <string name="hint_password">密码</string>
    <string name="cancel_title">取消</string>
    <string name="connect_title">连接</string>
    <string name="battery_status__critical_message">电池电量严重不足！\n请为设备充电。</string>
    <string name="battery_status_critical_message">电池电量严重不足！\n请为设备充电。</string>
    <string name="battery_charging_message">状态：充电中</string>
    <string name="set_region_msg">请设置区域</string>
    <string name="start_stop_progress_title">正在保存开始/停止触发器...</string>
    <string name="start_stop_triggers">开始/停止触发器</string>
    <string name="regulatory_progress_title">正在保存监管设置...</string>
    <string name="regulatory_settings">监管设置</string>
    <string name="antenna_progress_title">正在保存天线设置...</string>
    <string name="antenna_configuration">天线配置</string>
    <string name="singulation_progress_title">正在保存分离设置...</string>
    <string name="singulation_control">分离控制</string>
    <string name="save_config_progress_title">正在保存配置...</string>
    <string name="save_configuration">保存配置</string>
    <string name="tag_reporting_progress_title">正在保存标签报告设置...</string>
    <string name="tag_report_settings">设置报告配置</string>
    <string name="batch_mode_settings_title">批处理模式设置</string>
    <string name="beeper_progress_title">正在保存蜂鸣器设置...</string>
    <string name="set_beeper_volume">设置蜂鸣器音量</string>
    <string name="filter_progress_title">正在保存过滤器设置...</string>
    <string name="pre_filter">预过滤器命令</string>
    <string name="app_title">RFID 读取器</string>
    <string name="battery_no_active_connection_message">状态：无活动连接</string>
    <string name="status_failure_message">设置应用失败</string>
    <string name="battery_status_low_message">电池电量低！\n请为设备充电。</string>
    <string name="battery__status_low_message">电池电量低！\n请为设备充电。</string>
    <string name="battery_critical_message">状态：电池电量严重不足</string>
    <string name="battery_low_message">状态：电池电量低</string>
    <string name="battery_full_message">状态：电池已充满</string>
    <string name="default_timeout">10000</string>
    <string name="default_no_of_attempts">10</string>
    <string name="default_tag_observe_count">100</string>
    <string name="error_empty_fields_start_trigger">开始触发器设置中有空字段</string>
    <string name="error_empty_fields_stop_trigger">停止触发器设置中有空字段</string>
    <string name="error_empty_fields_preFilters">预过滤器中有空字段</string>
    <string name="empty_length">请填写长度</string>
    <string name="empty_offset">请填写偏移</string>
    <string name="dynamic_power_title">动态功率</string>
    <string name="export_data_sett">数据导出设置</string>
    <string name="export_data">导出数据</string>
    <string name="batch_mode_title">BT 批处理模式</string>
    <string name="usb_batch_mode_title">USB 批处理模式</string>
    <string name="scan_batch_mode">扫描批处理模式</string>
    <string name="batch_mode_inventory_title">库存正在批处理模式下运行</string>
    <string name="tag_report_settings_title">标签报告设置</string>
    <string name="title_activity_dpo_settings">电源管理</string>
    <string name="dpo_message_title">动态功率优化为最佳电池寿命配置读取器，并与预配置设置配合使用。动态功率优化仅适用于库存操作</string>
    <string name="batch_mode_running_title">正在批处理模式下运行</string>
    <string name="error_empty_fields_antenna_config">天线设置中有空字段</string>
    <string name="error_invalid_fields_antenna_config">天线设置中有无效字段</string>
    <string name="tari_title">Tari</string>
    <string name="dpo_progress_title">正在保存电源管理设置...</string>
    <string name="yes">是</string>
    <string name="no">否</string>
    <string name="action_bc">BC 扫描</string>
    <string name="action_advanced_op">高级选项</string>
    <string name="pref_beeper">蜂鸣器偏好</string>
    <string name="title_activity_main">MainActivity</string>
    <string name="enable_simple_filter_1">高级选项</string>
    <string name="enable_nom_filter_1">选择不匹配的标签</string>
    <string name="tag_list_match_mode">标签列表匹配模式</string>
    <string name="match_mode">匹配模式</string>
    <string name="bluetooth_config">蓝牙配置</string>
    <string name="csv_tag_details">详情</string>
    <string name="tag_match_complete">所有标签已匹配</string>
    <string name="export_config">导出</string>
    <string name="title_activity_load_configuration">加载配置</string>
    <string name="import_config">导入</string>
    <string name="export_config_progress_title">正在导出配置文件...</string>
    <string name="load_config_progress_title">正在加载配置文件...</string>
    <string name="load_config">加载</string>
    <string name="inventory_display_setting">库存显示设置</string>
    <string name="inventory_display_header_row">标题行</string>
    <string name="inventory_display_rssi_column">RSSI 列</string>
    <string name="tag_list_match_tag_names">显示友好名称</string>
    <string name="ascii_mode">ASCII 模式</string>
    <string name="sgtin_mode">SGTIN-96 模式</string>
    <string name="bluetooth_mode">蓝牙模式</string>
    <string name="enable_ascii_mode">启用 ASCII 模式</string>
    <string name="REQUIRES_TAGLIST_CSV">需要在 rfid 目录中有 taglist.csv 文件</string>
    <string name="PIE_title">PIE</string>
    <string name="action_locate">定位标签</string>
    <string name="action_tag_read_write">标签读/写</string>
    <string name="invalid_antenna_power">无效的天线功率值</string>
    <string name="loading_csv">正在从 CSV 文件导入标签列表</string>
    <string name="failed_settings">BrandId 设置应用失败</string>
    <string name="action_clear">清除</string>
    <string name="camera_scan_flash_warning">不要用闪光灯反射覆盖条形码</string>
    <string name="ml_kit_internet_connection">下载需要互联网连接。</string>

    <string-array name="inv_menu_items_for_matching_tags">
        <item>全部</item>
        <item>匹配</item>
        <item>缺失</item>
        <item>未知</item>
    </string-array>

    <string-array name="batch_modes_array">
        <item>禁用</item>
        <item>自动</item>
        <item>启用</item>
    </string-array>

    <string-array name="usb_batch_modes_array">
        <item>禁用</item>
        <item>启用</item>
    </string-array>

    <string-array name="pre_filter_type">
        <item>基本</item>
        <item>高级</item>
    </string-array>

    <string-array name="scan_batch_mode">
        <item>禁用</item>
        <item>自动</item>
        <item>启用</item>
    </string-array>

    <string name="dw_action">com.symbol.dwudiusertokens.udi</string>
    <string name="dw_category">zebra.intent.dwudiusertokens.UDI</string>
    <string name="datawedge_intent_key_source">com.symbol.datawedge.source</string>
    <string name="datawedge_intent_key_label_type">com.symbol.datawedge.label_type</string>
    <string name="datawedge_intent_key_data">com.symbol.datawedge.data_string</string>
    <string name="warning_title_ukrain_l">警告：</string>
    <string name="warning_text_ukrain_l">2W功率模式需要用户许可证。</string>
    <string name="action_inventory">盘点</string>
    <string name="global_settings">全局设置</string>
    <string name="error_bluetooth_disabled">请启用蓝牙</string>
    <string name="home_multiTag_locate">多标签定位</string>
    <string name="multiTag_reset_title">重置</string>
    <string name="multiTag_import_tags">+ 标签列表</string>
    <string name="multiTag_locate_taglist_already_imported">定位标签列表已导入</string>
    <string name="multiTag_locate_sett">多标签定位设置</string>
    <string name="multiTag_locate_sort">按接近度百分比排序</string>
    <string name="multiTag_locate_proximity_percent_title">标签发现接近度百分比</string>
    <string name="multiTag_locate_error_no_data_loaded">未加载标签项目</string>
    <string name="multiTag_locate_error_operation_running">多标签定位当前正在运行</string>
    <string name="multiTag_locate_add_item_success">成功添加标签项目</string>
    <string name="multiTag_locate_add_item_failed">添加标签项目失败</string>
    <string name="multiTag_locate_delete_item_success">成功删除标签项目</string>
    <string name="multiTag_locate_delete_item_failed">删除标签项目失败</string>
    <string name="warning_bt_enable_on_sled">在滑板设备上打开蓝牙</string>
    <string name="title_activity_home">扫描器控制</string>
    <string name="home_list_content_desc">右箭头</string>
    <string name="menu_item_device_disconnect">断开RFDXX连接</string>
    <string name="menu_item_device_pair">配对蓝牙RFDXX</string>
    <string name="version">版本</string>
    <string name="version_info">Zebra扫描器控制应用程序 v1.0\n\n©2019 Zebra Technologies Corp.和/或其附属公司。保留所有权利。\n</string>
    <string name="title_activity_scanners">可用设备列表</string>
    <string name="bt_scanners_not_found">未找到BT RFDXX</string>
    <string name="bt_no_scanners">未找到RFDXX。</string>
    <string name="menu_firmware_help">固件更新过程</string>
    <string name="menu_add_scanner">添加扫描器</string>
    <string name="menu_get_pairing_barcode">获取配对条码</string>
    <string name="menu_refresh_scanners">刷新</string>
    <string name="title_activity_scanner_configurations">连接帮助</string>
    <string name="title_activity_settings">应用设置</string>
    <string name="scanner_detection_title">扫描器检测</string>
    <string name="auto_detection">自动检测</string>
    <string name="pairing_barcode_title">配对新扫描器(STC)条码</string>
    <string name="pairing_barcode_type">条码类型</string>
    <string name="pairing_barcode_configuration">设置出厂默认值</string>
    <string name="reset_app_defaults">重置应用默认值</string>
    <string name="btn_reset_factory_defaults">重置默认值</string>
    <string name="events_title">后台通知</string>
    <string name="available_scanner">可用扫描器</string>
    <string name="active_scanner">活动扫描器</string>
    <string name="assertinfo">资产信息</string>
    <string name="barcode_event">条码事件</string>
    <string name="image_event">图像事件</string>
    <string name="video_event">视频事件</string>
    <string name="background_mode_title">后台模式</string>
    <string name="notifications">通知</string>
    <string name="title_activity_notifications">通知</string>
    <string name="notifications_title">通知</string>
    <string name="title_activity_battery_off">电池关闭</string>
    <string name="battery_off_content_desc">要扫描的条码</string>
    <string name="battery_off_text">扫描显示的条码以配置[电池关闭]模式</string>
    <string name="no_items">无项目</string>
    <string name="title_activity_base">BaseActivity</string>
    <string name="title_activity_test">TestActivity</string>
    <string name="title_activity_barcode">BarcodeActivity</string>
    <string name="title_activity_zxing_barcode">ZxingBarcodeActivity</string>
    <string name="title_activity_active_scanner">RFDXX</string>
    <string name="Disconnect">断开连接</string>
    <string name="info_title">信息</string>
    <string name="title_activity_beeper_actions">蜂鸣器设置</string>
    <string name="title_activity_led">LED控制</string>
    <string name="title_activity_assert">活动扫描器</string>
    <string name="title_activity_symbologies">符号设置(开/关)</string>
    <string name="title_activity_beeper_settings">蜂鸣器设置</string>
    <string name="title_activity_splash_screen">启动画面</string>
    <string name="title_activity_available_scanner">可用扫描器</string>
    <string name="title_activity_barcode_details">条码详情</string>
    <string name="title_activity_navigate_scan_speed_analytics">扫描速度分析(SSA)</string>
    <string name="title_activity_slowest_decode_image_settings">最慢解码图像设置</string>
    <string name="title_activity_scan_speed_analytics">扫描速度分析</string>
    <string name="title_activity_ssa_barcode_type">SSA条码类型</string>
    <string name="information">信息</string>
    <string name="id">ID</string>
    <string name="name">名称</string>
    <string name="scanner_id">扫描器ID：</string>
    <string name="barcode_type">条码类型：</string>
    <string name="barcode_data">条码数据：</string>
    <string name="beeper_volume">蜂鸣器音量</string>
    <string name="threshold_value">仅保存慢于以下时间的图像：</string>
    <string name="low">低</string>
    <string name="save_image">保存图像</string>
    <string name="medium">中</string>
    <string name="high">高</string>
    <string name="beeper_frequency">蜂鸣器频率</string>
    <string name="green_led_control">绿色LED</string>
    <string name="green_led_on">绿色LED开启</string>
    <string name="green_led_off">绿色LED关闭</string>
    <string name="amber_led_control">琥珀色/蓝色LED</string>
    <string name="amber_led_on">琥珀色/蓝色LED开启</string>
    <string name="amber_led_off">琥珀色/蓝色LED关闭</string>
    <string name="red_led_control">红色LED</string>
    <string name="red_led_on">红色LED开启</string>
    <string name="red_led_off">红色LED关闭</string>
    <string name="splash_content_desc">启动画面</string>
    <string name="upc_a">UPC-A</string>
    <string name="upc_e">UPC-E</string>
    <string name="upc_e1">UPC-E1</string>
    <string name="ean_8">EAN-8/JAN8</string>
    <string name="ean">EANJAN</string>
    <string name="ean_13">EAN-13/JAN13</string>
    <string name="bookland">Bookland EAN</string>
    <string name="code128">Code 128</string>
    <string name="composite">复合码</string>
    <string name="ucc">UCC/EAN-128</string>
    <string name="code39">Code 39</string>
    <string name="code93">Code 93</string>
    <string name="code11">Code 11</string>
    <string name="actions">操作</string>
    <string name="pull_trigger">扣动扳机</string>
    <string name="release_trigger">释放扳机</string>
    <string name="barcode_list">条码列表</string>
    <string name="no_barcode">未收到条码</string>
    <string name="connection">连接</string>
    <string name="auto_reconnection">重新连接到扫描器</string>
    <string name="beeper">蜂鸣器</string>
    <string name="scanning_control">扫描</string>
    <string name="content_desc">指示导航</string>
    <string name="led">LED</string>
    <string name="nav_desc">指示导航</string>
    <string name="symbologies">符号</string>
    <string name="enable_scanning">启用扫描</string>
    <string name="disable_scanning">禁用扫描</string>
    <string name="persist_settings">持久化设置</string>

    <string-array name="multiTag_found_proximity_percent_array">
        <item>100</item>
        <item>95</item>
        <item>90</item>
        <item>85</item>
        <item>80</item>
        <item>75</item>
        <item>70</item>
    </string-array>

    <string-array name="home_items">
        <item>设备</item>
        <item>连接帮助</item>
        <item>应用设置</item>
        <item>关于</item>
    </string-array>

    <string-array name="scanner_configs">
        <item>设置出厂默认值</item>
        <item>电池关闭</item>
        <item>底座主机</item>
        <item>SPP客户端</item>
        <item>SPP服务器</item>
        <item>HID服务器</item>
        <item>低蜂鸣器音量</item>
        <item>中蜂鸣器音量</item>
        <item>高蜂鸣器音量</item>
    </string-array>

    <string-array name="beeper_actions">
        <item>一声高音短蜂鸣</item>
        <item>两声高音短蜂鸣</item>
        <item>三声高音短蜂鸣</item>
        <item>四声高音短蜂鸣</item>
        <item>五声高音短蜂鸣</item>
        <item>一声低音短蜂鸣</item>
        <item>两声低音短蜂鸣</item>
        <item>三声低音短蜂鸣</item>
        <item>四声低音短蜂鸣</item>
        <item>五声低音短蜂鸣</item>
        <item>一声高音长蜂鸣</item>
        <item>两声高音长蜂鸣</item>
        <item>三声高音长蜂鸣</item>
        <item>四声高音长蜂鸣</item>
        <item>五声高音长蜂鸣</item>
        <item>一声低音长蜂鸣</item>
        <item>两声低音长蜂鸣</item>
        <item>三声低音长蜂鸣</item>
        <item>四声低音长蜂鸣</item>
        <item>五声低音长蜂鸣</item>
        <item>快速颤音蜂鸣</item>
        <item>慢速颤音蜂鸣</item>
        <item>高低音蜂鸣</item>
        <item>低高音蜂鸣</item>
        <item>高低高音蜂鸣</item>
        <item>低高低音蜂鸣</item>
        <item>高高低低音蜂鸣</item>
    </string-array>

    <string-array name="vibration_durations">
        <item>150</item>
        <item>200</item>
        <item>250</item>
        <item>300</item>
        <item>400</item>
        <item>500</item>
        <item>600</item>
        <item>750</item>
    </string-array>

    <string name="iso_file">以ISO格式接收的数据</string>
    <string name="model">型号：</string>
    <string name="serial">序列号：</string>
    <string name="firmware">固件：</string>
    <string name="config">配置文件名：</string>
    <string name="dateofmanufactured">生产日期：</string>
    <string name="aimon">AIM开启</string>
    <string name="aimoff">AIM关闭</string>
    <string name="vibration_feedback">振动反馈</string>
    <string name="title_activity_barcode_configuration">连接帮助</string>
    <string name="slowest_decode_image">最慢解码图像：</string>
    <string name="weight_measured">测量重量：</string>
    <string name="weight_Unit">重量单位：</string>
    <string name="weight_Status">重量状态：</string>
    <string name="live_Weight">实时重量：</string>
    <string name="top_hint">扫描连接</string>
    <string name="bt_logo">蓝牙标志</string>
    <string name="info_top">扫描下面的条码以将您的无线扫描器配对到此平板电脑/手机</string>
    <string name="info_bottom">此屏幕条码替代了纸质配对条码的需要。</string>
    <string name="clearList">清空列表</string>
    <string name="scale">秤</string>
    <string name="title_activity_scanner_list">ScannerListActivity</string>
    <string name="title_activity_connection_help">连接帮助</string>
    <string name="GS1128">GS1-128</string>
    <string name="GS1Datamatrix">GS1-数据矩阵</string>
    <string name="GS1QRCode">GS1-QR码</string>
    <string name="operationmode_dialog_title">选择操作模式</string>
    <string name="ok">确定</string>
    <string name="cancel_upper">取消</string>
    <string name="cancel">取消</string>
    <string name="error_occurred">发生错误！</string>
    <string name="error_occurred_no_permission">发生错误！权限被拒绝</string>
    <string name="disconnect_current_scanner">这将断开您当前的扫描器</string>
    <string name="image_saved">图像已成功保存到下载/扫描器目录</string>
    <string name="continue_txt">继续</string>
    <string name="instruction_snapi">将您的有线扫描器连接到设备并扫描条码以与此应用程序连接。</string>
    <string name="instruction">扫描条码以将您的无线扫描器配对到此应用程序。</string>
    <string name="pick_list_mode">选择列表模式</string>
    <string name="scanning">扫描控制</string>
    <string name="title_activity_connection_help_cs4070">ConnectionHelpCS4070</string>
    <string name="title_activity_connection_help_li4278">ConnectionHelpLI4278</string>
    <string name="title_activity_connection_help_rfd8500">ConnectionHelpRFD8500</string>
    <string name="change_path">插件文件路径不正确</string>
    <string name="change_path_plugin_msg_1">不要从默认快捷方式目录文件夹（如下载、文档、音乐等）中选择固件文件。从设备内部存储中选择相关文件夹。</string>
    <string name="change_path_plugin_msg_2">例如：如果文件位于下载文件夹中。\n<b>正确：</b>（<i>设备内部存储 > 下载 > 文件</i>）\n<b>错误：</b>（<i>下载(快捷方式) > 文件</i>）</string>
    <string name="change_path_csv">不要从默认快捷方式目录文件夹（如下载、文档、音乐等）中选择csv文件。从设备内部存储中选择相关文件夹。</string>
    <string name="selectFirmware">选择固件</string>
    <string name="updateFirmware">更新固件</string>
    <string name="Image_Video">图像和视频</string>
    <string name="IDC">IDC</string>
    <string name="start_new_firmware">启动新固件</string>
    <string name="abort_update_firmware">中止固件更新</string>
    <string name="title_activity_update_firmware">更新固件</string>
    <string name="update_firmware_confimation">确认您要更新以下扫描器</string>
    <string name="scanSpeedAnalytics">扫描速度分析</string>
    <string name="configure_ssa">配置SSA</string>
    <string name="view_ssa">查看SSA</string>
    <string name="trigger">触发器</string>
    <string name="led_control">LED控制</string>
    <string name="enable_disable">启用/禁用</string>
    <string name="aim_guide">瞄准指南</string>
    <string name="scale_enable">启用秤</string>
    <string name="test_beeper">测试蜂鸣器</string>
    <string name="imager_mode">图像</string>
    <string name="video_mode">视频</string>
    <string name="read_Weight">读取重量</string>
    <string name="live_weight_enable">启用实时重量</string>
    <string name="zero_scale">清零秤</string>
    <string name="reset_scale">重置秤</string>
    <string name="test_video">测试视频模式</string>
    <string name="test_idc">IDC：自由格式</string>
    <string name="view_finder">启用视频取景器</string>
    <string name="take_image">捕获图像</string>
    <string name="i2of5">交错2 of 5</string>
    <string name="_2of5">交错2 of 5</string>
    <string name="d2of5">离散2 of 5</string>
    <string name="c2of5">中文2 of 5</string>
    <string name="ocr">OCR</string>
    <string name="codabar">Codabar</string>
    <string name="msi">MSI</string>
    <string name="code32">Code 32</string>
    <string name="datamatrix">数据矩阵</string>
    <string name="pdf417">PDF417</string>
    <string name="pdf">PDF</string>
    <string name="isbn">ISBN</string>
    <string name="_other1D">其他1D</string>
    <string name="_other2D">其他2D</string>
    <string name="coupon">优惠券</string>
    <string name="digimarc_upc">Digimarc UPC</string>
    <string name="digimarc_ean_jan">Digimarc EAN/JAN</string>
    <string name="digimarc_other">Digimarc其他</string>
    <string name="other">其他</string>
    <string name="ucc_coupon_extended">UCC优惠券扩展码</string>
    <string name="issn_ean">ISSN EAN</string>
    <string name="isbt_128">ISBT 128</string>
    <string name="trioptic_code39">Trioptic Code 39</string>
    <string name="m2of5">矩阵2 of 5</string>
    <string name="k3of5">韩国3 of 5</string>
    <string name="gs1databar">GS1 DataBar</string>
    <string name="gs1databar14">GS1 DataBar-14</string>
    <string name="gs1databar_limited">GS1 DataBar Limited</string>
    <string name="gs1databar_expanded">GS1 DataBar Expanded</string>
    <string name="micro_pdf417">MicroPDF417</string>
    <string name="maxicode">Maxicode</string>
    <string name="qr">QR</string>
    <string name="qrcode">QR码</string>
    <string name="microqr">MicroQR</string>
    <string name="aztec">Aztec</string>
    <string name="hanxin_code">汉信码</string>
    <string name="au_post">澳大利亚邮政</string>
    <string name="us_planet">美国PLANET</string>
    <string name="us_postnet">美国POSTNET</string>
    <string name="kixcode">荷兰KIX</string>
    <string name="usps4cb">USPS 4CB</string>
    <string name="postalcodes">邮政编码</string>
    <string name="uk_postal">英国邮政</string>
    <string name="jp_postal">日本邮政</string>
    <string name="fics">UPU FICS</string>
    <string name="title_activity_vibration_feedback">振动反馈</string>
    <string name="vibration">振动</string>
    <string name="on_off">开/关</string>
    <string name="vibration_duration">振动持续时间(毫秒)</string>
    <string name="test_vibration">测试振动</string>
    <string name="battery_statistics">电池统计</string>
    <string name="scanner_name">扫描器名称：</string>
    <string name="title_activity_battery_statistics">电池统计</string>
    <string name="battery_asset_info">电池资产信息</string>
    <string name="no_battery_stats">此扫描器没有PowerPrecision+智能电池</string>
    <string name="no_scale">此扫描器没有秤</string>
    <string name="bat_manufacture_date">生产日期：</string>
    <string name="bat_serial">序列号：</string>
    <string name="bat_firmware_version">固件版本：</string>
    <string name="bat_design_cap">设计容量：</string>
    <string name="bat_state_of_health">健康状态：</string>
    <string name="battery_life_stat">电池寿命统计</string>
    <string name="bat_charge_cycles_consumed">已消耗充电周期：</string>
    <string name="battery_status">电池状态</string>
    <string name="bat_full_charge_cap">满电容量：</string>
    <string name="bat_state_of_charge">充电状态：</string>
    <string name="bat_remaining_cap">剩余容量：</string>
    <string name="bat_charge_status">充电状态：</string>
    <string name="bat_remaining_time_to_complete_charging">完成充电的剩余时间：</string>
    <string name="bat_voltage">电池电压</string>
    <string name="bat_current">电池电流：</string>
    <string name="battery_temperature">电池温度</string>
    <string name="bat_present">当前：</string>
    <string name="bat_highest">最高：</string>
    <string name="bat_lowest">最低：</string>
    <string name="release_notes">发布说明：</string>
    <string name="no_plugin">未找到ADAT文件</string>
    <string name="no_plugin_msg_1">将正确的DAT文件放在设备存储 > 下载文件夹中。</string>
    <string name="no_plugin_msg_2">有关说明，请点击右上角的帮助图标。</string>
    <string name="mismatch">插件/扫描器不匹配</string>
    <string name="mismatch_plugin_msg_1">1. 从设备存储 > 下载文件夹中删除错误的插件。</string>
    <string name="mismatch_plugin_msg_2">2. 将适用于您扫描器型号的正确插件加载到文件夹中。</string>
    <string name="firmware_not_support_ssa">此扫描器没有扫描速度分析功能</string>
    <string name="com_protocol_not_support_ssa">此通信协议不支持扫描速度分析</string>
    <string name="firmware_not_support_ssa_msg_1">将扫描器更新为支持扫描速度分析的固件。</string>
    <string name="fw_update_process">固件更新过程帮助</string>
    <string name="fw_update_process_msg_1">将适用于您扫描器的正确123Scan插件复制到您的手机：</string>
    <string name="fw_update_process_msg_1_1">1. 从以下位置将123Scan加载到Windows计算机：</string>
    <string name="fw_update_process_url">www.Zebra.com/123Scan</string>
    <string name="fw_update_process_msg_1_2">2. 从装有123Scan的Windows PC，从C:\\ProgramData\\123Scan2\\Plug-ins访问您扫描器的插件(.scnplg文件)</string>
    <string name="fw_update_process_msg_1_3">3. 将插件的副本放入您手机的下载文件夹（设备存储 > 下载）</string>
    <string name="fw_update_process_msg_2">通过点击"更新固件"按钮开始固件更新：</string>
    <string name="close">关闭</string>
    <string name="updating_firmware">正在更新固件</string>
    <string name="rebooting_scanner">正在重启。请勿拔出电池。</string>
    <string name="update_failed">更新失败</string>
    <string name="firmware_updated">✓ 固件已更新</string>
    <string name="last_connected_scanner">最后连接的扫描器</string>
    <string name="other_scanners">其他扫描器</string>
    <string name="txt_continue">继续</string>
    <string name="launch_instruction">此应用程序支持扫描连接技术，用于一步配对。</string>
    <string name="launch_instruction_item_2">• 启用/禁用符号</string>
    <string name="launch_instruction_item_1">• 编程蜂鸣器和LED</string>
    <string name="launch_instruction_item_3">• 远程触发扫描</string>
    <string name="launch_instruction_remaining">它显示扫描的条码数据。</string>
    <string name="launch_instruction_remaining_2">它可以查询扫描器资产信息和电池健康统计。</string>
    <string name="dont_show_this_again">不再显示此消息。</string>
    <string name="com_protocol">通信协议</string>
    <string name="reconnect_scanner">重新连接扫描器</string>
    <string name="launch_instruction2">它允许您控制您的扫描器：</string>
    <string name="supported_scanners">支持的扫描器</string>
    <string name="pairing_help_all_scanners">配对帮助 - 所有扫描器</string>
    <string name="pairing_help_rfd8500">配对帮助 - 仅限RFD8500</string>
    <string name="title_activity_supported_scanners">支持的扫描器</string>
    <string name="supported_scanners_title">以下扫描器可用于此应用程序：</string>
    <string name="title_activity_supported_scanners_cordless_1">无线（</string>
    <string name="title_activity_supported_scanners_cordless_2"> > 配对新蓝牙）</string>
    <string name="title_activity_supported_scanners_corded_1">有线扫描器*（</string>
    <string name="title_activity_supported_scanners_corded_2"> > 查找有线扫描器）</string>
    <string name="details_activity_supported_scanners_corded">* 对于有线扫描器支持，需要完全供电的USB主端口</string>
    <string name="supported_scanners_li3678">• LI3678</string>
    <string name="supported_scanners_rfd8500">• RFD8500</string>
    <string name="supported_scanners_ds3678">• DS3678</string>
    <string name="supported_scanners_cs4070">• CS4070（Rev E固件及更新版本）</string>
    <string name="supported_scanners_ds8178">• DS8178</string>
    <string name="supported_scanners_ds2278">• DS2278</string>
    <string name="supported_scanners_cs6080">• CS6080</string>
    <string name="supported_scanners_rs5100">• RS5100</string>
    <string name="supported_scanners_mp6000">• MP6000</string>
    <string name="supported_scanners_mp7000">• MP7000</string>
    <string name="supported_scanners_mx101">• MP6/7000和MX101</string>
    <string name="supported_scanners_ds8178_mp6010">• 通过CR8178的MP6/7000和DS8178</string>
    <string name="supported_scanners_ds3608">• DS3608</string>
    <string name="supported_scanners_ds8108">• DS8108</string>
    <string name="supported_scanners_ds9808">• DS9808</string>
    <string name="supported_scanners_ds4308">• DS4308</string>
    <string name="supported_scanners_ds2208">• DS2208</string>
    <string name="supported_scanners_ds7708">• DS7708</string>
    <string name="supported_scanners_ls2208">• LS2208（MFD > 2015年1月）</string>
    <string name="supported_scanners_se4757">• SE4757</string>
    <string name="title_activity_pairing_instructions_all">配对说明</string>
    <string name="all_scanners_pairing_instructions_title">所有扫描器 - 配对说明</string>
    <string name="all_scanners_pairing_instructions_1">1. 扫描"设置出厂默认值"条码</string>
    <string name="all_scanners_pairing_instructions_2">2. 在"配对新扫描器"屏幕上重新扫描条码</string>
    <string name="all_scanners_pairing_instructions_3">3. 扫描"配对新扫描器"条码</string>
    <string name="set_factory_defaults">设置出厂默认值</string>
    <string name="title_activity_pairing_instructions_rfd8500">配对说明</string>
    <string name="rfd8500_pairing_instructions_title">RFD8500配对说明</string>
    <string name="rfd8500_pairing_instructions_1">1. 打开RFD8500并确保蓝牙已启用。</string>
    <string name="rfd8500_pairing_instructions_2">2. 在Android设备上启用蓝牙。</string>
    <string name="rfd8500_pairing_instructions_3">3. Android设备将发现RFD8500并在蓝牙设备列表中显示它。</string>
    <string name="rfd8500_pairing_instructions_1_1">• RFD8500在启动后40秒内可通过蓝牙发现。之后蓝牙暂停且不再可发现。要再次使其可发现，请按RFD8500侧面的蓝牙按钮。</string>
    <string name="rfd8500_pairing_instructions_4">4. 从蓝牙设备列表中点击"RFD8500"以启动配对。</string>
    <string name="rfd8500_pairing_instructions_5">5. 当蓝牙LED开始快速闪烁时，按RFD8500扳机完成配对。</string>
    <string name="rfd8500_pairing_instructions_6">6. 启动"扫描器控制"应用程序/菜单/"可用设备列表"并选择RFD8500。</string>
    <string name="multiple_dat_files">多个DAT文件</string>
    <string name="multiple_plugin_files">多个插件文件</string>
    <string name="multiple_dat_files_msg_1">在下载文件夹中找到多个DAT文件。请只保留一个DAT文件并重试。</string>
    <string name="no_pager_motor">此扫描器没有寻呼机电机。</string>
    <string name="title_activity_find_cabled_scanner">查找有线扫描器</string>
    <string name="multiple_snapi_devices">您已经在SNAPI协议中。要选择设备请点击继续。</string>
    <string name="find_scanner">查找扫描器</string>
    <string name="sample_barcodes">扫描示例条码</string>
    <string name="title_activity_sample_barcodes">SampleBarcodes</string>
    <string name="sample_barcodes_instructions">从下面的列表中扫描示例条码，或扫描您自己的条码。</string>
    <string name="upc">UPC：</string>
    <string name="gs1_data_bar_stacked">GS1 - DataBar堆叠</string>
    <string name="multiple_plug_in_files_msg_1">在下载文件夹中找到多个插件文件。请只保留一个插件文件并重试。</string>
    <string name="beeper_sequence">蜂鸣器序列</string>
    <string name="selct_code">选择代码类型</string>
    <string name="get_bt_address_instruction1">请输入您的蓝牙地址。</string>
    <string name="get_bt_address_instruction_item_1">导航到"设置 > 关于手机 > 状态 > 蓝牙地址"或</string>
    <string name="get_bt_address_instruction_item_2">然后记下蓝牙地址。</string>
    <string name="get_bt_address_instruction_item_3">按返回按钮直到返回此屏幕。</string>
    <string name="get_bt_address_instruction_item_4">然后在下面输入地址。</string>
    <string name="about_phone">转到关于手机</string>
    <string name="cancel2">取消</string>
    <string name="bluetooth_address">蓝牙地址</string>
    <string name="clear">清除</string>
    <string name="skip">跳过\n（如果是USB有线）</string>
    <string name="enable">启用</string>
    <string name="ssa_slowest_decode_image">最慢解码图像：</string>
    <string name="ssa_slowest_decode_time">最慢解码时间（毫秒）：</string>
    <string name="ssa_slowest_decode_data">最慢解码数据：</string>
    <string name="ssa_scan_speed_histogram">扫描速度直方图</string>
    <string name="ssa_total_scans">总扫描数：</string>
    <string name="ssa_char_x_axis_title">解码时间（毫秒）</string>
    <string name="ssa_char_y_axis_title">扫描次数</string>
    <string name="ssa_reset_analytics">重置分析</string>
    <string name="ssa_no_data_text">开始扫描\n条码进行\n分析</string>
    <string name="ssa_resetting">正在重置分析</string>
    <string name="ssa_image_transfer_not_supported">蓝牙低功耗目前不支持图像传输。</string>
    <string name="title_activity_image_video">图像和视频</string>
    <string name="title_activity_idc">IDC</string>
    <string name="title_activity_app_home">AppHomeActivity</string>
    <string name="dummy_button">虚拟按钮</string>
    <string name="dummy_content">虚拟\n内容</string>
    <string name="first_fragment_label">第一个片段</string>
    <string name="second_fragment_label">第二个片段</string>
    <string name="next">下一个</string>
    <string name="previous">上一个</string>
    <string name="hello_first_fragment">你好第一个片段</string>
    <string name="hello_second_fragment">你好第二个片段。参数：%1$s</string>
    <string name="rfid_title">RFID</string>
    <string name="scanner_title">条码扫描</string>
    <string name="title_app_overview">应用概览</string>
    <string name="title_discovery">设备发现</string>
    <string name="toast_scanner_not_attached">警告：未连接扫描器</string>
    <string name="action_scan">扫描视图</string>
    <string name="title_nfc_pair">配对</string>
    <string name="title_nfc_instruction">点击NFC标签</string>
    <string name="scanTrigger">扫描</string>
    <string name="batch_request">批量请求</string>
    <string name="factory_reset">执行出厂重置将清除所有保存的设置并重启读取器。需要重新设置区域。</string>
    <string name="factory_heading">重置为出厂默认值</string>
    <string name="resetFactory_progress_title">设备出厂重置</string>
    <string name="radio_heading">执行无线电重置将重启读取器。</string>
    <string name="radio_reset">重置读取器</string>
    <string name="operation_success_message">操作成功</string>
    <string name="operation_failure_message">操作失败</string>
    <string name="firmwareupdate">固件更新</string>
    <string name="FactoryReset">出厂重置</string>
    <string name="devicereset">设备重置</string>
    <string name="deviceinfo">设备信息</string>
    <string name="EnableLogging">启用日志记录</string>
    <string name="ApplicationSettings">应用程序</string>
    <string name="regulatory">监管测试</string>
    <string name="KeyMapping">触发器映射</string>
    <string name="KeyMappingSelect">RFID触发器选择</string>
    <string name="Logger">日志记录器</string>
    <string name="Realtime">启用实时日志</string>
    <string name="Bufferedlogs">检索缓冲日志</string>
    <string name="InternalRamLogs">从RAM检索日志</string>
    <string name="InternalFlashLogs">从闪存检索日志</string>
    <string name="NGEErrorlogs">启用NGE错误日志</string>
    <string name="NGEEventlogs">启用NGE事件日志</string>
    <string name="NGEPacketlogs">启用NGE数据包日志</string>
    <string name="ApplicationLogger">应用程序</string>
    <string name="Enable">启用调试日志</string>
    <string name="Export_Logs">导出日志</string>
    <string name="sdkversiontitle">SDK版本</string>
    <string name="scannerversiontitle">扫描器版本</string>
    <string name="title_empty_readers">读取器</string>
    <string name="batterylife">电池寿命</string>
    <string name="paired_reader">已配对读取器</string>
    <string name="serial_no">显示序列号</string>
    <string name="readermodel">型号：%1$s</string>
    <string name="RFID">RFID</string>
    <string name="SCAN">扫描</string>
    <string name="generalSettings">常规</string>
    <string name="finish">完成</string>
    <string name="serialno">序列号：%1$s</string>
    <string name="no_nfc_support">此设备型号不支持NFC</string>
    <string name="counter"></string>
    <string name="Location_Alert_title">警告：</string>
    <string name="warning_text_location_enable">此功能需要启用/使用位置服务来执行RFID设备的蓝牙发现。</string>
    <string name="nav_help_instruction_1">123RFID移动版展示了RFID功能，如配置您的RFID手持读取器、读取和定位RFID标签等。</string>
    <string name="nav_help_instruction_2">使用123RFID移动版，您可以</string>
    <string name="nav_help_instruction_item1">• 从移动终端配对和连接读取器</string>
    <string name="nav_help_instruction_item2">• 运行盘点</string>
    <string name="nav_help_instruction_item3">• 定位单个或多个RFID标签</string>
    <string name="nav_help_instruction_item4">• 更新读取器固件</string>
    <string name="nav_help_instruction_item5">• 检查电池健康</string>
    <string name="nav_version_info">123RFID移动应用程序 v1.0\n\n©2021 Zebra Technologies Corp.和/或其附属公司。保留所有权利。\n</string>
    <string name="title_activity_navigation_help">NavigationHelpActivity</string>
    <string name="err_read_access_op_failed">标签读取操作失败</string>
    <string name="update_failed_low_battery_level">（电池百分比低于20%）</string>
    <string name="update_failed_commands_are_out_of_sync">（命令不同步）</string>
    <string name="update_failed_has_overlapping_address">（固件有重叠地址）</string>
    <string name="update_failed_load_count_error">（加载计数错误）</string>
    <string name="wificonnectdetails">您是否要与您的\n%2$s读取器共享\n%1$s的访问权限？</string>
    <string name="scan_settings">扫描设置</string>
    <string name="scan_adv__settings">高级扫描设置</string>
    <string name="DeviceReset">设备重置</string>
    <string name="nonPremium">此功能不支持此设备</string>
    <string name="network_ip_Config">网络IP配置</string>
    <string name="ipSettings">IP设置</string>
    <string name="ip_address">IP地址</string>
    <string name="netmask">子网掩码</string>
    <string name="gateway">网关</string>
    <string name="DNS">DNS</string>
    <string name="scan_and_pair_help">扫描和配对过程</string>
    <string name="PairOperationsHelp">配对操作指南</string>
    <string name="radio">RFID无线电</string>
    <string name="compositec">复合C</string>
    <string name="compositeAB">复合AB</string>
    <string name="tlc39">TLC39</string>
    <string name="dotcode">点码</string>
    <string name="NFC_pairing_help">将读取器的NFC标签点击到必须配对的设备的NFC标签</string>
    <string name="Scan_pairing_help">用户需要从扫描和配对屏幕扫描手持设备上的条码，这将开始自动连接或必须输入序列号或蓝牙MAC地址</string>
    <string name="Camera_barcode_scan_help">点击扫描按钮并扫描滑板的序列号条码</string>

    <string-array name="expandable_drawer">
        <item>可用设备列表</item>
        <item>管理RFDXXX</item>
        <item>固件更新</item>
        <item>应用概览</item>
    </string-array>

    <string-array name="manager_rfdxxx_drawer">
        <item>重置出厂默认值</item>
        <item>重置读取器</item>
        <item>启用日志记录</item>
        <item>导出配置</item>
        <item>生成报告</item>
    </string-array>

    <string name="MESSettings">主机/MES系统配置</string>
    <string name="ServerAddress">服务器地址</string>
    <string name="EnterServerAddress">请输入服务器地址</string>
    <string name="Port">端口</string>
    <string name="EnterPort">请输入端口号</string>
    <string name="ConnectionType">连接类型</string>
    <string name="Save">保存</string>
    <string name="RetryCount">重试次数</string>
    <string name="EnterRetryCount">输入重试次数(0-10)</string>

    <string name="language_setting">语言</string>
    <string-array name="language_options">
        <item>English</item>
        <item>中文</item>
    </string-array>
    
    <!-- 语言切换对话框 -->
    <string name="language_change_title">语言设置</string>
    <string name="language_change_message">语言设置已更改。应用程序需要重启才能生效。现在重启？</string>
    <string name="restart">重启</string>

    <!-- 语言验证 -->
    <string name="language_validation">语言验证</string>
    <string name="validate_language_settings">验证</string>

    <!-- MES设置 -->
    <string name="mes_settings_title">MES设置</string>
    <string name="server_address">服务器地址</string>
    <string name="port_number">端口号</string>
    <string name="retry_count">重试次数</string>
    <string name="connection_type">连接类型</string>
    <string name="save_settings">保存设置</string>
    <string name="please_fill_all_required_fields">请填写所有必填项</string>
    <string name="port_must_be_between_1_65535">端口号必须在1-65535之间</string>
    <string name="retry_count_must_be_between_0_10">重试次数必须在0-10之间</string>
    <string name="settings_saved">设置已保存</string>
    <string name="port_and_retry_must_be_valid_numbers">端口号和重试次数必须是有效的数字</string>

    <!-- MES通信 -->
    <string name="scan_result_empty">扫描结果为空</string>
    <string name="communication_instance_not_initialized">通信实例未初始化</string>
    <string name="unable_to_establish_server_connection">无法建立服务器连接</string>
    <string name="connection_status_abnormal">连接状态异常，无法发送数据</string>
    <string name="send_operation_timeout">发送操作超时，请检查网络连接</string>
    <string name="send_operation_interrupted">发送操作被中断</string>
    <string name="not_connected_to_server">未连接到服务器</string>
    <string name="connection_timeout">连接超时</string>
    <string name="connection_failed">连接失败</string>
    <string name="send_interrupted">发送被中断</string>
    <string name="unknown_connection_type">未知的连接类型</string>
    <string name="tcp_connection_not_established">TCP连接未建立或已关闭</string>
    <string name="no_response_from_server">未收到服务器响应，但数据已发送成功</string>
    <string name="send_failed_retried_times">发送失败 (已重试%d次): %s</string>
    <string name="unknown_error">未知错误</string>

    <!-- 连接类型 -->
    <string-array name="connection_types">
        <item>TCP</item>
        <item>UDP</item>
        <item>HTTP</item>
        <item>HTTPS</item>
    </string-array>

    <!-- 快速读取片段 -->
    <string name="mes_server_connected">MES服务器连接成功</string>
    <string name="mes_server_disconnected">MES服务器连接断开</string>
    <string name="mes_server_connection_failed">MES服务器连接失败: %s</string>
    <string name="received_data">收到数据: %s</string>
    <string name="connection_failed_retrying">连接失败，正在重试 (%d/%d): %s</string>
    <string name="reconnection_successful">重连成功 (第%d次尝试)</string>
    <string name="connection_failed_after_retries">连接失败，已重试%d次: %s</string>
    <string name="unable_to_get_activity">无法获取Activity</string>
    <string name="mes_system_config_info">MES系统配置信息:\n服务器地址: %s\n端口: %s\n连接类型: %s\n重试次数: %d次</string>
    <string name="mes_server_not_configured">未配置上位机/MES系统服务地址信息\n请前往设置页面配置服务器信息</string>
    <string name="failed_to_read_mes_config">读取MES配置信息失败</string>
    <string name="max_scan_time_reached">达到最大扫描时间，停止扫描</string>
    <string name="max_tag_limit_reached">达到最大标签数量限制，停止扫描</string>
    <string name="connecting_to_mes_server">正在连接MES服务器...</string>
    <string name="preparing_to_send_rfid_to_mes">准备发送RFID结果到MES</string>
    <string name="rfid_send_result">RFID发送结果\n%s</string>
    <string name="mes_send_error">MES发送出错: %s</string>
    <string name="mes_communication_not_initialized">MES通信未初始化</string>
    <string name="scan_result_label">扫码结果: %s</string>
    <string name="preparing_to_send_scan_to_mes">准备发送扫码结果到MES: %s</string>
    <string name="scan_send_result">SCAN发送结果\n%s</string>
</resources>