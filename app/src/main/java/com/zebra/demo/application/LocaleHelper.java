package com.zebra.demo.application;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;

import com.zebra.demo.R;

import java.util.Locale;

/**
 * 语言管理工具类
 * 用于处理应用的语言切换和本地化
 */
public class LocaleHelper {
    private static final String TAG = "LocaleHelper";
    private static final String LANGUAGE_KEY = "language_key";
    private static final String LANGUAGE_EN = "en";
    private static final String LANGUAGE_ZH = "zh";

    /**
     * 设置应用语言
     *
     * @param context  上下文
     * @param language 语言代码 ("en" 或 "zh")
     * @return 更新后的上下文
     */
    public static Context setLocale(Context context, String language) {
        return setLocale(context, language, false);
    }

    /**
     * 设置应用语言
     *
     * @param context  上下文
     * @param language 语言代码 ("en" 或 "zh")
     * @param persist  是否保存语言设置
     * @return 更新后的上下文
     */
    public static Context setLocale(Context context, String language, boolean persist) {
        if (persist) {
            persistLanguage(context, language);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context, language);
        }

        return updateResourcesLegacy(context, language);
    }

    /**
     * 获取当前语言
     *
     * @param context 上下文
     * @return 当前语言代码
     */
    public static String getLanguage(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        String savedLanguage = prefs.getString(LANGUAGE_KEY, LANGUAGE_EN); // 默认使用英文

        Log.d(TAG, "Current language: " + savedLanguage);
        return savedLanguage;
    }

    /**
     * 初始化语言设置（仅在首次启动时调用）
     *
     * @param context 上下文
     */
    public static void initializeLanguageOnFirstRun(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        boolean isFirstRun = prefs.getBoolean("is_first_run", true);

        if (isFirstRun) {
            Log.d(TAG, "First run detected, checking system language");

            // 检测系统语言
            String systemLanguage = getSystemLanguage();
            Log.d(TAG, "System language detected: " + systemLanguage);

            // 根据系统语言设置应用语言
            String appLanguage;
            if (systemLanguage.startsWith("zh")) {
                appLanguage = LANGUAGE_ZH;
                Log.d(TAG, "System is Chinese, setting app language to Chinese");
            } else {
                appLanguage = LANGUAGE_EN;
                Log.d(TAG, "System is not Chinese, setting app language to English");
            }

            // 保存语言设置
            persistLanguage(context, appLanguage);

            // 立即应用语言设置，确保界面和设置一致
            setLocale(context, appLanguage);

            // 标记不再是首次运行
            prefs.edit().putBoolean("is_first_run", false).apply();

            Log.d(TAG, "First run initialization completed, language set to: " + appLanguage);
        } else {
            Log.d(TAG, "Not first run, keeping existing language setting: " + getLanguage(context));
        }
    }

    /**
     * 获取当前语言索引
     * 
     * @param context 上下文
     * @return 语言索引 (0: 英文, 1: 中文)
     */
    public static int getLanguageIndex(Context context) {
        String language = getLanguage(context);
        return LANGUAGE_ZH.equals(language) ? 1 : 0;
    }

    /**
     * 根据索引设置语言
     *
     * @param context 上下文
     * @param index   语言索引 (0: 英文, 1: 中文)
     * @return 更新后的上下文
     */
    public static Context setLanguageByIndex(Context context, int index) {
        String language = (index == 1) ? LANGUAGE_ZH : LANGUAGE_EN;
        Log.d(TAG, "Setting language by index: " + index + " -> " + language);

        // 先保存语言设置
        persistLanguage(context, language);

        // 验证保存是否成功
        String savedLanguage = getLanguage(context);
        Log.d(TAG, "Language saved verification: " + savedLanguage);

        // 确保保存成功
        if (!language.equals(savedLanguage)) {
            Log.e(TAG, "Language save failed! Expected: " + language + ", Actual: " + savedLanguage);
            // 重试保存
            persistLanguage(context, language);
        }

        // 然后应用语言设置
        return setLocale(context, language);
    }

    /**
     * 获取系统语言
     *
     * @return 系统语言代码
     */
    private static String getSystemLanguage() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return Resources.getSystem().getConfiguration().getLocales().get(0).getLanguage();
        } else {
            return Resources.getSystem().getConfiguration().locale.getLanguage();
        }
    }

    /**
     * 持久化语言设置
     *
     * @param context  上下文
     * @param language 语言代码
     */
    public static void persistLanguage(Context context, String language) {
        try {
            SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(LANGUAGE_KEY, language);

            // 使用commit()而不是apply()确保立即保存
            boolean success = editor.commit();

            if (success) {
                Log.d(TAG, "Language saved successfully: " + language);
            } else {
                Log.e(TAG, "Failed to save language: " + language);
                // 重试一次
                editor.putString(LANGUAGE_KEY, language).commit();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error saving language: " + e.getMessage());
        }
    }

    /**
     * 更新资源 (API 24+)
     * 
     * @param context  上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    private static Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Configuration configuration = new Configuration(context.getResources().getConfiguration());
        configuration.setLocale(locale);

        return context.createConfigurationContext(configuration);
    }

    /**
     * 更新资源 (API 23 及以下)
     * 
     * @param context  上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    private static Context updateResourcesLegacy(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Resources resources = context.getResources();
        Configuration configuration = new Configuration(resources.getConfiguration());
        configuration.locale = locale;
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());

        return context;
    }

    /**
     * 检查是否需要重启应用
     * 
     * @param context          上下文
     * @param newLanguageIndex 新的语言索引
     * @return 是否需要重启
     */
    public static boolean needsRestart(Context context, int newLanguageIndex) {
        int currentIndex = getLanguageIndex(context);
        return currentIndex != newLanguageIndex;
    }

    /**
     * 获取语言显示名称
     *
     * @param context 上下文
     * @param index   语言索引
     * @return 语言显示名称
     */
    public static String getLanguageDisplayName(Context context, int index) {
        String[] languages = context.getResources().getStringArray(R.array.language_options);
        if (index >= 0 && index < languages.length) {
            return languages[index];
        }
        return languages[0]; // 默认返回英文
    }

    /**
     * 检测当前界面实际使用的语言
     *
     * @param context 上下文
     * @return 当前界面语言代码
     */
    public static String getCurrentDisplayLanguage(Context context) {
        try {
            // 方法1: 检查当前Configuration的Locale
            Configuration config = context.getResources().getConfiguration();
            String currentLocale;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                currentLocale = config.getLocales().get(0).getLanguage();
            } else {
                currentLocale = config.locale.getLanguage();
            }

            Log.d(TAG, "Current configuration locale: " + currentLocale);

            // 方法2: 通过检查特定字符串资源来判断当前显示的语言
            String appName = context.getString(R.string.app_name);
            boolean hasChineseChars = appName.matches(".*[\\u4e00-\\u9fa5].*");

            Log.d(TAG, "App name: " + appName + ", has Chinese chars: " + hasChineseChars);

            // 优先使用Configuration的语言设置
            if (currentLocale.startsWith("zh")) {
                return LANGUAGE_ZH;
            } else if (hasChineseChars) {
                // 如果Configuration不是中文但显示中文字符，说明可能有问题
                Log.w(TAG, "Configuration locale is " + currentLocale + " but displaying Chinese characters");
                return LANGUAGE_ZH;
            } else {
                return LANGUAGE_EN;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error detecting display language: " + e.getMessage());
            return LANGUAGE_EN; // 默认返回英文
        }
    }

    /**
     * 强制重置语言为英文
     *
     * @param context 上下文
     */
    public static void forceResetToEnglish(Context context) {
        Log.d(TAG, "Force resetting language to English");
        persistLanguage(context, LANGUAGE_EN);
        setLocale(context, LANGUAGE_EN);
        // 同时重置首次运行标志
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        prefs.edit().putBoolean("is_first_run", false).apply();
    }

    /**
     * 强制禁用首次运行检测，确保不再自动同步系统语言
     *
     * @param context 上下文
     */
    public static void disableFirstRunDetection(Context context) {
        Log.d(TAG, "Disabling first run detection to prevent auto language sync");
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        prefs.edit().putBoolean("is_first_run", false).apply();
    }

    /**
     * 强制重置为首次运行状态（用于测试）
     *
     * @param context 上下文
     */
    public static void resetToFirstRun(Context context) {
        Log.d(TAG, "Resetting to first run state");
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        prefs.edit()
                .putBoolean("is_first_run", true)
                .remove(LANGUAGE_KEY)
                .apply();
    }

    /**
     * 强制同步语言设置（已禁用，避免覆盖用户选择）
     * 检查当前显示的语言和保存的语言设置是否一致，如果不一致则修复
     *
     * @param context 上下文
     */
    public static void syncLanguageSettings(Context context) {
        // 禁用自动同步，避免覆盖用户的语言选择
        Log.d(TAG, "syncLanguageSettings called but disabled to preserve user choice");

        String currentDisplayLanguage = getCurrentDisplayLanguage(context);
        String savedLanguage = getLanguage(context);

        Log.d(TAG, "Current display language: " + currentDisplayLanguage + ", Saved language: " + savedLanguage);

        // 不再自动更新，只记录日志
        // if (!currentDisplayLanguage.equals(savedLanguage)) {
        //     Log.d(TAG, "Language mismatch detected, updating saved language to: " + currentDisplayLanguage);
        //     persistLanguage(context, currentDisplayLanguage);
        // }
    }
}