package com.zebra.demo.mes_settings;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.io.*;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.RejectedExecutionException;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import android.provider.Settings;
import android.os.Build;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import org.json.JSONArray;

public class MESCommunication {
    private static final String TAG = "MESCommunication";
    private static final int TCP_TIMEOUT = 5000; // 5 seconds
    private static final int RECONNECT_DELAY = 5000; // 5 seconds
    private static final int MAX_RECONNECT_ATTEMPTS = 5;
    private static final int RETRY_DELAY = 1000; // 1 second
    private static final int SEND_TIMEOUT = 30000; // 30 seconds
    private static final int READ_TIMEOUT = 5000; // 5 seconds
    private static final int WRITE_TIMEOUT = 5000; // 5 seconds
    private static final int MAX_RETRY_COUNT = 3; // 最大重试次数
    private static final int RETRY_INTERVAL = 2000; // 重试间隔2秒
    private static final int RESPONSE_TIMEOUT_MULTIPLIER = 3; // 响应超时倍数
    private static final int RESPONSE_WAIT_TIME = 1000; // 响应等待时间1秒
    private static final int RESPONSE_CHECK_INTERVAL = 500; // 响应检查间隔500毫秒
    private static final boolean WAIT_FOR_RESPONSE = true; // 是否等待响应（设为false避免超时问题）
    private static final int INITIAL_READ_TIMEOUT = 3000; // 初始读取超时3秒
    private static final int SECONDARY_READ_TIMEOUT = 2000; // 二次读取超时2秒

    private Context context;
    private String serverAddress;
    private int port;
    private int connectionType; // 0: TCP, 1: UDP, 2: HTTP, 3: HTTPS
    private int retryCount; // 发送失败重试次数
    private Socket tcpSocket;
    private boolean isConnected;
    private boolean isReconnecting;
    private int reconnectAttempts;
    private ExecutorService executorService;
    private Handler mainHandler;
    private ConnectionCallback connectionCallback;
    private final Object sendLock = new Object(); // 发送同步锁
    private volatile boolean isReleased = false; // 添加释放状态标志

    public interface ConnectionCallback {
        void onConnected();

        void onDisconnected();

        void onConnectionFailed(String error);

        void onDataReceived(String data);

        // 新增连接重试状态回调
        void onConnectionRetrying(int attempt, int maxAttempts, String error);

        void onConnectionRetrySuccess(int attempt);

        void onConnectionRetryFailed(int maxAttempts, String finalError);
    }

    public MESCommunication(Context context) {
        this.context = context;
        this.executorService = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        loadSettings();
    }

    private void loadSettings() {
        SharedPreferences sharedPreferences = context.getSharedPreferences("MESSettings", 0);
        serverAddress = sharedPreferences.getString("server_address", "");
        port = Integer.parseInt(sharedPreferences.getString("port", "0"));
        connectionType = sharedPreferences.getInt("connection_type", 0);
        retryCount = sharedPreferences.getInt("retry_count", 3); // 默认重试3次
    }

    public void setConnectionCallback(ConnectionCallback callback) {
        this.connectionCallback = callback;
    }

    public void connect() {
        if (isConnected || isReleased) {
            return;
        }

        // 检查executorService是否仍然可用
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
            Log.e(TAG, "Executor service is not available for connection");
            return;
        }

        try {
            executorService.execute(() -> {
                try {
                    switch (connectionType) {
                        case 0: // TCP
                            connectTCP();
                            break;
                        case 1: // UDP
                            connectUDP();
                            break;
                        case 2: // HTTP
                            connectHTTP();
                            break;
                        case 3: // HTTPS
                            connectHTTPS();
                            break;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "========= ERROR ============ Connection failed: " + e.getMessage());
                    notifyConnectionFailed(e.getMessage());
                    if (!isReleased) {
                        startReconnection();
                    }
                }
            });
        } catch (RejectedExecutionException e) {
            Log.e(TAG, "Executor service rejected connection task: " + e.getMessage());
        }
    }

    private void connectTCP() throws IOException {
        Log.d(TAG, "正在尝试TCP连接到 " + serverAddress + ":" + port);
        tcpSocket = new Socket();
        try {
            tcpSocket.connect(new InetSocketAddress(serverAddress, port), TCP_TIMEOUT);
            tcpSocket.setSoTimeout(READ_TIMEOUT);
            tcpSocket.setTcpNoDelay(true);
            tcpSocket.setKeepAlive(true);
            isConnected = true;
            reconnectAttempts = 0;
            Log.d(TAG, "TCP连接成功");
            notifyConnected();

            // Start TCP keep-alive thread
            startTCPKeepAlive();
        } catch (IOException e) {
            Log.e(TAG, "========= ERROR ============ TCP connection failed: " + e.getMessage());
            throw e;
        }
    }

    private void connectUDP() {
        // UDP implementation
        isConnected = true;
        notifyConnected();
    }

    private void connectHTTP() {
        // HTTP implementation
        isConnected = true;
        notifyConnected();
    }

    private void connectHTTPS() {
        // HTTPS implementation
        isConnected = true;
        notifyConnected();
    }

    private void startTCPKeepAlive() {
        // 检查executorService是否仍然可用
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated() || isReleased) {
            Log.e(TAG, "Executor service is not available for TCP keep-alive");
            return;
        }

        try {
            executorService.execute(() -> {
                while (isConnected && !isReleased) {
                    try {
                        if (tcpSocket != null && !tcpSocket.isClosed()) {
                            tcpSocket.getOutputStream().write(0);
                        } else {
                            throw new IOException("Socket closed");
                        }
                        Thread.sleep(30000); // Send keep-alive every 30 seconds
                    } catch (Exception e) {
                        Log.e(TAG, "========= ERROR ============ TCP keep-alive failed: " + e.getMessage());
                        if (!isReleased) {
                            handleConnectionLost();
                        }
                        break;
                    }
                }
            });
        } catch (RejectedExecutionException e) {
            Log.e(TAG, "Executor service rejected TCP keep-alive task: " + e.getMessage());
        }
    }

    private void handleConnectionLost() {
        isConnected = false;
        notifyDisconnected();
        startReconnection();
    }

    private void startReconnection() {
        if (isReconnecting || isReleased) {
            Log.d(TAG, "重连已在进行中或实例已释放");
            return;
        }

        isReconnecting = true;
        reconnectAttempts = 0;
        Log.d(TAG, "开始重连过程");

        // 检查executorService是否仍然可用
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
            Log.e(TAG, "Executor service is not available for reconnection");
            isReconnecting = false;
            return;
        }

        try {
            executorService.execute(() -> {
                while (isReconnecting && reconnectAttempts < MAX_RECONNECT_ATTEMPTS && !isReleased) {
                    try {
                        Log.d(TAG,
                                "等待 " + RECONNECT_DELAY + "ms 后进行重连尝试 "
                                        + (reconnectAttempts + 1));
                        Thread.sleep(RECONNECT_DELAY);
                        reconnectAttempts++;
                        Log.d(TAG, "尝试重连 " + reconnectAttempts + "/" + MAX_RECONNECT_ATTEMPTS);
                        connect();
                        if (isConnected) {
                            Log.d(TAG, "重连成功");
                            isReconnecting = false;
                            break;
                        }
                    } catch (Exception e) {
                        Log.e(TAG,
                                "========= ERROR ============ Reconnection attempt " + reconnectAttempts + " failed: "
                                        + e.getMessage());
                    }
                }

                if (!isConnected && !isReleased) {
                    Log.e(TAG, "========= ERROR ============ Failed to reconnect after " + MAX_RECONNECT_ATTEMPTS
                            + " attempts");
                    isReconnecting = false;
                    notifyConnectionFailed("Failed to reconnect after " + MAX_RECONNECT_ATTEMPTS + " attempts");
                }
            });
        } catch (RejectedExecutionException e) {
            Log.e(TAG, "Executor service rejected reconnection task: " + e.getMessage());
            isReconnecting = false;
        }
    }

    public void disconnect() {
        isReconnecting = false;
        isConnected = false;

        // 检查executorService是否仍然可用
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated() || isReleased) {
            // 直接关闭socket，不使用executor
            try {
                if (tcpSocket != null && !tcpSocket.isClosed()) {
                    tcpSocket.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "========= ERROR ============ Error closing socket: " + e.getMessage());
            }
            return;
        }

        try {
            executorService.execute(() -> {
                try {
                    if (tcpSocket != null && !tcpSocket.isClosed()) {
                        tcpSocket.close();
                    }
                } catch (IOException e) {
                    Log.e(TAG, "========= ERROR ============ Error closing socket: " + e.getMessage());
                }
            });
        } catch (RejectedExecutionException e) {
            Log.e(TAG, "Executor service rejected disconnect task: " + e.getMessage());
            // 直接关闭socket
            try {
                if (tcpSocket != null && !tcpSocket.isClosed()) {
                    tcpSocket.close();
                }
            } catch (IOException ioException) {
                Log.e(TAG, "========= ERROR ============ Error closing socket: " + ioException.getMessage());
            }
        }
    }

    public boolean isConnected() {
        return isConnected;
    }

    public void sendData(String data) {
        // 检查是否已释放
        if (isReleased) {
            Log.e(TAG, "MESCommunication instance has been released");
            return;
        }

        // 发送数据前先检查连接状态
        if (!isConnected) {
            Log.e(TAG, "发送数据前检查：未连接到服务器");
            notifyConnectionFailed("Not connected to server");
            return;
        }

        // 再次确认连接状态
        if (tcpSocket == null || tcpSocket.isClosed()) {
            Log.e(TAG, "发送数据前检查：TCP连接无效");
            notifyConnectionFailed("TCP connection is invalid");
            return;
        }

        // 检查executorService是否仍然可用
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
            Log.e(TAG, "Executor service is not available for sending data");
            return;
        }

        try {
            executorService.execute(() -> {
                try {
                    Log.d(TAG, "开始发送数据...");

                    switch (connectionType) {
                        case 0: // TCP
                            sendTCPData(data);
                            break;
                        case 1: // UDP
                            sendUDPData(data);
                            break;
                        case 2: // HTTP
                            sendHTTPData(data);
                            break;
                        case 3: // HTTPS
                            sendHTTPSData(data);
                            break;
                    }

                    Log.d(TAG, "数据发送完成");

                    // 发送完成后断开连接
                    Log.d(TAG, "发送完成，断开连接");
                    disconnect();

                } catch (Exception e) {
                    Log.e(TAG, "========= ERROR ============ 发送数据时发生错误: " + e.getMessage());
                    if (!isReleased) {
                        handleConnectionLost();
                    }

                    // 发生错误时也要断开连接
                    Log.d(TAG, "发送错误，断开连接");
                    disconnect();
                }
            });
        } catch (RejectedExecutionException e) {
            Log.e(TAG, "Executor service rejected send data task: " + e.getMessage());
        }
    }

    private void sendTCPData(String data) throws IOException {
        if (tcpSocket != null && !tcpSocket.isClosed()) {
            try {
                java.io.OutputStream out = tcpSocket.getOutputStream();
                // 添加数据长度前缀
                String lengthPrefix = String.format("%04d", data.length());
                // 发送数据长度
                out.write(lengthPrefix.getBytes());
                // 发送实际数据
                out.write(data.getBytes());
                // 添加结束标记
                out.write("\r\n".getBytes());
                out.flush();

                Log.d(TAG, "TCP数据发送完成，长度: " + data.length());
                Log.d(TAG, "发送的数据: " + data);

                // 等待服务器响应
                BufferedReader reader = new BufferedReader(new InputStreamReader(tcpSocket.getInputStream()));
                String response = reader.readLine();
                if (response != null) {
                    Log.d(TAG, "收到服务器响应: " + response);
                    notifyDataReceived(response);
                }
            } catch (IOException e) {
                Log.e(TAG, "========= ERROR ============ TCP数据发送失败: " + e.getMessage());
                throw e;
            }
        } else {
            throw new IOException("TCP连接未建立或已关闭");
        }
    }

    private void sendUDPData(String data) {
        // UDP implementation
    }

    private void sendHTTPData(String data) throws IOException {
        URL url = new URL("http://" + serverAddress + ":" + port);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setConnectTimeout(TCP_TIMEOUT);

        try (java.io.OutputStream os = conn.getOutputStream()) {
            os.write(data.getBytes());
            os.flush();
        }

        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String response = br.readLine();
                notifyDataReceived(response);
            }
        }
    }

    private void sendHTTPSData(String data) throws IOException {
        URL url = new URL("https://" + serverAddress + ":" + port);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setConnectTimeout(TCP_TIMEOUT);

        try (java.io.OutputStream os = conn.getOutputStream()) {
            os.write(data.getBytes());
            os.flush();
        }

        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String response = br.readLine();
                notifyDataReceived(response);
            }
        }
    }

    private void notifyConnected() {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onConnected());
        }
    }

    private void notifyDisconnected() {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onDisconnected());
        }
    }

    private void notifyConnectionFailed(String error) {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onConnectionFailed(error));
        }
    }

    private void notifyDataReceived(String data) {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onDataReceived(data));
        }
    }

    private void notifyConnectionRetrying(int attempt, int maxAttempts, String error) {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onConnectionRetrying(attempt, maxAttempts, error));
        }
    }

    private void notifyConnectionRetrySuccess(int attempt) {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onConnectionRetrySuccess(attempt));
        }
    }

    private void notifyConnectionRetryFailed(int maxAttempts, String finalError) {
        if (connectionCallback != null) {
            mainHandler.post(() -> connectionCallback.onConnectionRetryFailed(maxAttempts, finalError));
        }
    }

    public void release() {
        Log.d(TAG, "正在释放MESCommunication资源...");

        // 设置释放标志，防止新的任务提交
        isReleased = true;

        // 停止重连
        isReconnecting = false;

        // 断开连接
        disconnect();

        // 关闭executor service
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(2, TimeUnit.SECONDS)) {
                    Log.w(TAG, "执行器服务未正常终止，强制关闭");
                    executorService.shutdownNow();
                    if (!executorService.awaitTermination(1, TimeUnit.SECONDS)) {
                        Log.e(TAG, "Executor service did not terminate");
                    }
                }
            } catch (InterruptedException e) {
                Log.w(TAG, "等待执行器服务终止时被中断");
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        Log.d(TAG, "MESCommunication资源已释放");
    }

    /**
     * 发送扫描结果到MES系统
     * 
     * @param scanResult 扫描结果数据
     * @param type       扫描类型（scan/rfid）
     * @return 发送结果通知
     */
    public String sendScanResult(String scanResult, String type) {
        if (scanResult == null || scanResult.trim().isEmpty()) {
            return type.toUpperCase() + " : 扫描结果为空";
        }

        if (context == null) {
            Log.e(TAG, "========= ERROR ============ MESCommunication实例未正确初始化，context为空");
            return "通信实例未初始化";
        }

        final String[] result = new String[1];
        final CountDownLatch latch = new CountDownLatch(1);

        // 使用新的线程池执行发送任务
        ExecutorService sendExecutor = Executors.newSingleThreadExecutor();
        sendExecutor.execute(() -> {
            try {
                // 显示开始连接的信息
                Log.d(TAG, "开始建立MES服务器连接...");

                // 发送数据前先检查连接状态
                if (!ensureConnection()) {
                    result[0] = "无法建立服务器连接";
                    latch.countDown();
                    return;
                }

                // 再次确认连接状态
                if (!isConnected) {
                    result[0] = "连接状态异常，无法发送数据";
                    latch.countDown();
                    return;
                }

                // 构建发送数据
                String data = buildScanData(scanResult, type);
                Log.d(TAG, "准备发送数据: " + data);

                // 执行发送并等待响应返回结果
                result[0] = sendWithRetry(data);
                Log.d(TAG, "发送完成，收到响应: " + result[0]);

                // 发送完成后断开连接
                Log.d(TAG, "发送完成，断开连接");
                disconnect();

            } catch (Exception e) {
                Log.e(TAG, "发送数据时发生错误: " + e.getMessage());
                result[0] = "发送失败: " + e.getMessage();
                // 发生错误时也要断开连接
                Log.d(TAG, "发生错误，断开连接");
                disconnect();
            } finally {
                latch.countDown();
                sendExecutor.shutdown();
            }
        });

        try {
            if (!latch.await(SEND_TIMEOUT, TimeUnit.MILLISECONDS)) {
                Log.e(TAG, "发送操作超时");
                // 超时时也要断开连接
                Log.d(TAG, "发送超时，断开连接");
                disconnect();
                return "发送操作超时，请检查网络连接";
            }
            return result[0];
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // 中断时也要断开连接
            Log.d(TAG, "发送被中断，断开连接");
            disconnect();
            return "发送操作被中断";
        }
    }

    /**
     * 确保连接状态
     * 
     * @return 连接是否成功
     */
    private boolean ensureConnection() {
        if (isConnected) {
            return true;
        }

        Log.d(TAG, "未连接到服务器，尝试建立连接...");
        loadSettings();

        // 获取连接重试次数配置
        int connectionRetryCount = retryCount; // 使用MES配置中的重试次数
        int currentAttempt = 0;
        Exception lastException = null;

        while (currentAttempt <= connectionRetryCount) {
            currentAttempt++;

            try {
                Log.d(TAG, String.format("尝试连接服务器 (第%d次，共%d次)", currentAttempt, connectionRetryCount + 1));

                // 通知重试状态
                if (currentAttempt > 1) {
                    String errorMsg = lastException != null ? lastException.getMessage() : "连接失败";
                    notifyConnectionRetrying(currentAttempt, connectionRetryCount + 1, errorMsg);
                }

                connect();

                // 等待连接建立
                int waitCount = 0;
                while (!isConnected && waitCount < 10) {
                    Thread.sleep(500);
                    waitCount++;
                }

                if (isConnected) {
                    Log.d(TAG, "连接成功");
                    if (currentAttempt > 1) {
                        notifyConnectionRetrySuccess(currentAttempt);
                    }
                    return true;
                } else {
                    throw new Exception("连接超时");
                }

            } catch (Exception e) {
                lastException = e;
                Log.e(TAG, String.format("连接失败 (尝试 %d/%d): %s",
                        currentAttempt, connectionRetryCount + 1, e.getMessage()));

                // 如果不是最后一次尝试，等待后重试
                if (currentAttempt <= connectionRetryCount) {
                    try {
                        Log.d(TAG, String.format("等待%d秒后重试连接", RETRY_INTERVAL / 1000));
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        Log.e(TAG, "连接重试被中断");
                        break;
                    }
                }
            }
        }

        // 所有重试都失败了
        String finalError = lastException != null ? lastException.getMessage() : "连接失败";
        Log.e(TAG, String.format("连接失败，已重试%d次: %s", connectionRetryCount, finalError));
        notifyConnectionRetryFailed(connectionRetryCount + 1, finalError);
        return false;
    }

    private String sendWithRetry(String data) {
        int currentRetry = 0;
        Exception lastException = null;

        while (currentRetry <= retryCount) {
            try {
                Log.d(TAG, String.format("尝试发送数据 (第%d次)", currentRetry + 1));

                // 使用同步锁确保数据发送的原子性
                synchronized (sendLock) {
                    switch (connectionType) {
                        case 0: // TCP
                            return sendTCPDataWithResponse(data);
                        case 1: // UDP
                            sendUDPData(data);
                            return "success";
                        case 2: // HTTP
                            return sendHTTPDataWithResponse(data);
                        case 3: // HTTPS
                            return sendHTTPSDataWithResponse(data);
                        default:
                            return "未知的连接类型";
                    }
                }
            } catch (Exception e) {
                lastException = e;
                Log.e(TAG, String.format("发送失败 (尝试 %d/%d): %s",
                        currentRetry + 1, retryCount + 1, e.getMessage()));

                // 检查是否是响应超时错误
                if (e.getMessage() != null && e.getMessage().contains("超时")) {
                    Log.d(TAG, "检测到响应超时，等待更长时间后重试");
                    if (currentRetry < retryCount) {
                        try {
                            // 响应超时时等待更长时间
                            Thread.sleep(RETRY_INTERVAL * 2);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            return "发送被中断";
                        }
                    }
                } else {
                    // 其他错误类型，使用标准重试间隔
                    if (currentRetry < retryCount) {
                        try {
                            Thread.sleep(RETRY_INTERVAL);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            return "发送被中断";
                        }
                    }
                }
                currentRetry++;
            }
        }

        handleConnectionLost();
        return String.format("发送失败 (已重试%d次): %s",
                retryCount, lastException != null ? lastException.getMessage() : "未知错误");
    }

    private String sendTCPDataWithResponse(String data) throws IOException {
        return sendTCPDataWithResponse(data, false); // 默认使用标准方法
    }

    private String sendTCPDataWithResponse(String data, boolean useEnhancedReader) throws IOException {
        if (tcpSocket == null || tcpSocket.isClosed()) {
            throw new IOException("TCP连接未建立或已关闭");
        }

        int retryCount = 0;
        IOException lastException = null;

        while (retryCount < MAX_RETRY_COUNT) {
            try {
                // 设置socket选项 - 在发送前设置，避免发送后修改超时
                tcpSocket.setSoTimeout(READ_TIMEOUT * RESPONSE_TIMEOUT_MULTIPLIER); // 设置较长的读取超时
                tcpSocket.setSendBufferSize(data.length() + 10);
                tcpSocket.setReceiveBufferSize(1024);
                tcpSocket.setTcpNoDelay(true);
                tcpSocket.setKeepAlive(true);

                java.io.OutputStream out = tcpSocket.getOutputStream();

                // 添加数据长度前缀
                String lengthPrefix = String.format("%04d", data.length());
                byte[] lengthBytes = lengthPrefix.getBytes();
                byte[] dataBytes = data.getBytes();
                byte[] endBytes = "\r\n".getBytes();

                // 分步发送数据
                // out.write(lengthBytes);
                // out.flush();
                // Thread.sleep(50);

                out.write(dataBytes);
                out.flush();
                Thread.sleep(50);

                out.write(endBytes);
                out.flush();

                Log.d(TAG, "TCP数据发送完成，长度: " + data.length());
                Log.d(TAG, "发送的数据: " + data);

                // 等待服务器响应 - 根据参数选择读取方法
                String response;
                if (useEnhancedReader) {
                    response = readTCPResponseEnhanced();
                } else {
                    response = readTCPResponse();
                }

                if (response != null) {
                    return response;
                }

                // 如果没有收到响应，认为发送成功
                Log.w(TAG, "未收到服务器响应，但数据已发送成功");
                return "success";

            } catch (IOException e) {
                Log.e(TAG, "TCP数据发送失败: " + e.getMessage());
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRY_COUNT) {
                    try {
                        Log.d(TAG, String.format("发送失败，等待%d秒后重试", RETRY_INTERVAL / 1000));
                        Thread.sleep(RETRY_INTERVAL);
                        reconnectTCP();
                        continue;
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("发送被中断", ie);
                    }
                }
                throw e;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("发送被中断", e);
            }
        }

        throw new IOException("发送失败，已重试" + MAX_RETRY_COUNT + "次: " +
                (lastException != null ? lastException.getMessage() : "未知错误"));
    }

    /**
     * 读取TCP响应的专用方法
     */
    private String readTCPResponse() {
        // 如果配置为不等待响应，直接返回null
        if (!WAIT_FOR_RESPONSE) {
            Log.d(TAG, "配置为不等待响应，跳过响应读取");
            return null;
        }

        try {
            Log.d(TAG, "=== 开始TCP响应读取 ===");
            Log.d(TAG, "Socket状态: " + (tcpSocket != null ? (tcpSocket.isClosed() ? "已关闭" : "已打开") : "未创建"));

            if (tcpSocket == null || tcpSocket.isClosed()) {
                Log.e(TAG, "Socket无效，无法读取响应");
                return null;
            }

            // 检查Socket是否可读
            if (!tcpSocket.isConnected()) {
                Log.e(TAG, "Socket未连接，无法读取响应");
                return null;
            }

            // 创建新的输入流读取器
            java.io.InputStream inputStream = tcpSocket.getInputStream();
            StringBuilder responseBuilder = new StringBuilder();

            Log.d(TAG, "开始等待服务器响应...");
            Log.d(TAG, "初始读取超时: " + INITIAL_READ_TIMEOUT + "ms");

            // 设置初始超时时间
            tcpSocket.setSoTimeout(INITIAL_READ_TIMEOUT);

            // 第一次尝试读取响应 - 使用字节读取而不是行读取
            try {
                Log.d(TAG, "第一次尝试读取响应...");

                // 检查是否有数据可读
                int available = inputStream.available();
                if (available > 0) {
                    Log.d(TAG, "✅检测到输入流有数据可读: " + available + " bytes");

                    // 读取所有可用数据
                    byte[] buffer = new byte[available];
                    int bytesRead = inputStream.read(buffer);
                    if (bytesRead > 0) {
                        String rawResponse = new String(buffer, 0, bytesRead, "UTF-8");
                        Log.d(TAG, "✅读取到原始响应数据: [" + rawResponse + "]");
                        responseBuilder.append(rawResponse);
                    }
                } else {
                    Log.d(TAG, "输入流暂无数据可读");
                }

                // 尝试使用BufferedReader读取更多数据（如果有换行符）
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                String line;
                while ((line = reader.readLine()) != null) {
                    Log.d(TAG, "✅✅读取到响应行: [" + line + "]");

                    // 如果遇到空行，表示响应结束
                    if (line.trim().isEmpty()) {
                        Log.d(TAG, "遇到空行，响应结束");
                        break;
                    }

                    responseBuilder.append(line);

                    // 如果响应行包含结束标记，也结束读取
                    if (line.contains("END") || line.contains("}")) {
                        Log.d(TAG, "检测到响应结束标记");
                        break;
                    }
                }
            } catch (IOException e) {
                Log.d(TAG, "第一次读取响应超时或异常: " + e.getMessage());
                // 超时是正常的，很多服务器不发送响应
            }

            String response = responseBuilder.toString().trim();
            if (!response.isEmpty()) {
                Log.d(TAG, "✅收到完整服务器响应: [" + response + "]");
                notifyDataReceived(response);

                // 尝试解析JSON响应
                try {
                    JSONObject jsonResponse = new JSONObject(response);
                    if (jsonResponse.has("status")) {
                        String status = jsonResponse.getString("status");
                        if ("success".equalsIgnoreCase(status)) {
                            return jsonResponse.getString("data");
                        } else if (jsonResponse.has("message")) {
                            return jsonResponse.getString("message");
                        }
                    }
                    return response;
                } catch (Exception e) {
                    Log.w(TAG, "响应不是有效的JSON格式: " + e.getMessage());
                    return response;
                }
            }

            // 如果没有立即收到响应，等待一段时间再尝试
            Log.d(TAG, "未立即收到响应，等待额外时间...");
            Thread.sleep(1000);

            // 检查Socket状态
            if (tcpSocket.isClosed()) {
                Log.e(TAG, "Socket在等待期间被关闭");
                return null;
            }

            // 重置超时时间，再次尝试读取
            tcpSocket.setSoTimeout(SECONDARY_READ_TIMEOUT);
            Log.d(TAG, "第二次读取超时: " + SECONDARY_READ_TIMEOUT + "ms");

            try {
                Log.d(TAG, "第二次尝试读取响应...");

                // 再次检查是否有数据可读
                int available = inputStream.available();
                if (available > 0) {
                    Log.d(TAG, "延迟检测到输入流有数据可读: " + available + " bytes");

                    // 读取所有可用数据
                    byte[] buffer = new byte[available];
                    int bytesRead = inputStream.read(buffer);
                    if (bytesRead > 0) {
                        String rawResponse = new String(buffer, 0, bytesRead, "UTF-8");
                        Log.d(TAG, "延迟读取到原始响应数据: [" + rawResponse + "]");
                        responseBuilder.append(rawResponse);
                    }
                }

                // 再次尝试行读取
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                String line;
                while ((line = reader.readLine()) != null) {
                    Log.d(TAG, "延迟读取到响应行: [" + line + "]");

                    if (line.trim().isEmpty()) {
                        break;
                    }

                    responseBuilder.append(line);

                    if (line.contains("END") || line.contains("}")) {
                        break;
                    }
                }
            } catch (IOException e) {
                Log.d(TAG, "第二次读取响应超时或异常: " + e.getMessage());
            }

            response = responseBuilder.toString().trim();
            if (!response.isEmpty()) {
                Log.d(TAG, "延迟收到服务器响应: [" + response + "]");
                notifyDataReceived(response);

                try {
                    JSONObject jsonResponse = new JSONObject(response);
                    if (jsonResponse.has("status")) {
                        String status = jsonResponse.getString("status");
                        if ("success".equalsIgnoreCase(status)) {
                            return jsonResponse.getString("data");
                        } else if (jsonResponse.has("message")) {
                            return jsonResponse.getString("message");
                        }
                    }
                    return response;
                } catch (Exception e) {
                    Log.w(TAG, "延迟响应不是有效的JSON格式: " + e.getMessage());
                    return response;
                }
            }

            // 最终没有收到响应
            Log.w(TAG, "=== TCP响应读取完成，未收到响应 ===");
            return null;

        } catch (Exception e) {
            Log.e(TAG, "读取响应时发生异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 增强版TCP响应读取方法 - 处理各种数据格式
     */
    private String readTCPResponseEnhanced() {
        if (!WAIT_FOR_RESPONSE) {
            Log.d(TAG, "配置为不等待响应，跳过响应读取");
            return null;
        }

        try {
            Log.d(TAG, "=== 开始增强版TCP响应读取 ===");

            if (tcpSocket == null || tcpSocket.isClosed() || !tcpSocket.isConnected()) {
                Log.e(TAG, "Socket状态异常，无法读取响应");
                return null;
            }

            java.io.InputStream inputStream = tcpSocket.getInputStream();
            StringBuilder responseBuilder = new StringBuilder();

            // 设置较短的超时时间，避免长时间阻塞
            tcpSocket.setSoTimeout(2000); // 2秒超时

            // 多次尝试读取，每次读取间隔100ms
            for (int attempt = 1; attempt <= 5; attempt++) {
                try {
                    Log.d(TAG, "第" + attempt + "次尝试读取响应...");

                    // 检查可用数据
                    int available = inputStream.available();
                    Log.d(TAG, "可用字节数: " + available);

                    if (available > 0) {
                        // 读取可用数据
                        byte[] buffer = new byte[Math.min(available, 1024)]; // 最多读取1KB
                        int bytesRead = inputStream.read(buffer);

                        if (bytesRead > 0) {
                            String chunk = new String(buffer, 0, bytesRead, "UTF-8");
                            Log.d(TAG, "读取到数据块: [" + chunk + "]");
                            responseBuilder.append(chunk);

                            // 检查是否收到完整响应
                            String currentResponse = responseBuilder.toString();
                            if (isCompleteResponse(currentResponse)) {
                                Log.d(TAG, "检测到完整响应");
                                break;
                            }
                        }
                    }

                    // 短暂等待
                    Thread.sleep(100);

                } catch (IOException e) {
                    Log.d(TAG, "第" + attempt + "次读取超时: " + e.getMessage());
                    // 超时是正常的，继续下一次尝试
                }
            }

            String finalResponse = responseBuilder.toString().trim();
            if (!finalResponse.isEmpty()) {
                Log.d(TAG, "最终响应: [" + finalResponse + "]");
                notifyDataReceived(finalResponse);
                return finalResponse;
            }

            Log.w(TAG, "未收到任何响应数据");
            return null;

        } catch (Exception e) {
            Log.e(TAG, "增强版响应读取异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 判断响应是否完整
     */
    private boolean isCompleteResponse(String response) {
        if (response == null || response.isEmpty()) {
            return false;
        }

        // 检查常见的响应结束标记
        if (response.contains("END") ||
                response.contains("}") ||
                response.contains("\n") ||
                response.contains("\r\n") ||
                response.endsWith("OK") ||
                response.endsWith("SUCCESS")) {
            return true;
        }

        // 检查JSON格式是否完整
        try {
            new JSONObject(response);
            return true;
        } catch (Exception e) {
            // 不是完整JSON，继续等待
        }

        return false;
    }

    private void reconnectTCP() throws IOException {
        Log.d(TAG, "尝试重新建立TCP连接");
        if (tcpSocket != null && !tcpSocket.isClosed()) {
            try {
                tcpSocket.close();
            } catch (IOException e) {
                Log.w(TAG, "关闭旧连接失败: " + e.getMessage());
            }
        }
        connectTCP();
    }

    private String sendHTTPDataWithResponse(String data) throws IOException {
        int retryCount = 0;
        IOException lastException = null;

        while (retryCount < MAX_RETRY_COUNT) {
            try {
                URL url = new URL("http://" + serverAddress + ":" + port);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setDoOutput(true);
                conn.setConnectTimeout(TCP_TIMEOUT);
                conn.setReadTimeout(READ_TIMEOUT * RESPONSE_TIMEOUT_MULTIPLIER); // 延长读取超时时间
                conn.setRequestProperty("Content-Type", "application/json");
                conn.setRequestProperty("Accept", "application/json");

                Log.d(TAG, "HTTP请求URL: " + url.toString());
                Log.d(TAG, "HTTP发送数据: " + data);

                try (java.io.OutputStream os = conn.getOutputStream()) {
                    os.write(data.getBytes());
                    os.flush();
                }

                int responseCode = conn.getResponseCode();
                Log.d(TAG, "HTTP响应码: " + responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                        StringBuilder responseBuilder = new StringBuilder();
                        String line;

                        // 读取完整响应
                        while ((line = br.readLine()) != null) {
                            responseBuilder.append(line);
                        }

                        String response = responseBuilder.toString().trim();
                        if (!response.isEmpty()) {
                            Log.d(TAG, "HTTP收到服务器响应: " + response);
                            notifyDataReceived(response);

                            // 尝试解析JSON响应
                            try {
                                JSONObject jsonResponse = new JSONObject(response);
                                if (jsonResponse.has("status")) {
                                    String status = jsonResponse.getString("status");
                                    if ("success".equalsIgnoreCase(status)) {
                                        return "success";
                                    } else if (jsonResponse.has("message")) {
                                        return jsonResponse.getString("message");
                                    }
                                }
                                return response;
                            } catch (Exception e) {
                                Log.w(TAG, "HTTP响应不是有效的JSON格式: " + e.getMessage());
                                return response;
                            }
                        } else {
                            Log.w(TAG, "HTTP请求成功但响应为空");
                            return "success"; // 某些服务器可能不返回响应内容
                        }
                    }
                } else {
                    // 尝试读取错误响应
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getErrorStream()))) {
                        StringBuilder errorBuilder = new StringBuilder();
                        String line;
                        while ((line = br.readLine()) != null) {
                            errorBuilder.append(line);
                        }
                        String errorResponse = errorBuilder.toString().trim();
                        Log.e(TAG, "HTTP错误响应: " + errorResponse);
                        throw new IOException("HTTP请求失败，响应码: " + responseCode + ", 错误: " + errorResponse);
                    } catch (Exception e) {
                        throw new IOException("HTTP请求失败，响应码: " + responseCode);
                    }
                }
            } catch (IOException e) {
                Log.e(TAG, "HTTP数据发送失败: " + e.getMessage());
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRY_COUNT) {
                    try {
                        Log.d(TAG, String.format("HTTP发送失败，等待%d秒后重试", RETRY_INTERVAL / 1000));
                        Thread.sleep(RETRY_INTERVAL);
                        continue;
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("HTTP发送被中断", ie);
                    }
                }
                throw e;
            }
        }

        throw new IOException("HTTP发送失败，已重试" + MAX_RETRY_COUNT + "次: " +
                (lastException != null ? lastException.getMessage() : "未知错误"));
    }

    private String sendHTTPSDataWithResponse(String data) throws IOException {
        int retryCount = 0;
        IOException lastException = null;

        while (retryCount < MAX_RETRY_COUNT) {
            try {
                URL url = new URL("https://" + serverAddress + ":" + port);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setDoOutput(true);
                conn.setConnectTimeout(TCP_TIMEOUT);
                conn.setReadTimeout(READ_TIMEOUT * RESPONSE_TIMEOUT_MULTIPLIER); // 延长读取超时时间
                conn.setRequestProperty("Content-Type", "application/json");
                conn.setRequestProperty("Accept", "application/json");

                Log.d(TAG, "HTTPS请求URL: " + url.toString());
                Log.d(TAG, "HTTPS发送数据: " + data);

                try (java.io.OutputStream os = conn.getOutputStream()) {
                    os.write(data.getBytes());
                    os.flush();
                }

                int responseCode = conn.getResponseCode();
                Log.d(TAG, "HTTPS响应码: " + responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                        StringBuilder responseBuilder = new StringBuilder();
                        String line;

                        // 读取完整响应
                        while ((line = br.readLine()) != null) {
                            responseBuilder.append(line);
                        }

                        String response = responseBuilder.toString().trim();
                        if (!response.isEmpty()) {
                            Log.d(TAG, "HTTPS收到服务器响应: " + response);
                            notifyDataReceived(response);

                            // 尝试解析JSON响应
                            try {
                                JSONObject jsonResponse = new JSONObject(response);
                                if (jsonResponse.has("status")) {
                                    String status = jsonResponse.getString("status");
                                    if ("success".equalsIgnoreCase(status)) {
                                        return "success";
                                    } else if (jsonResponse.has("message")) {
                                        return jsonResponse.getString("message");
                                    }
                                }
                                return response;
                            } catch (Exception e) {
                                Log.w(TAG, "HTTPS响应不是有效的JSON格式: " + e.getMessage());
                                return response;
                            }
                        } else {
                            Log.w(TAG, "HTTPS请求成功但响应为空");
                            return "success"; // 某些服务器可能不返回响应内容
                        }
                    }
                } else {
                    // 尝试读取错误响应
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getErrorStream()))) {
                        StringBuilder errorBuilder = new StringBuilder();
                        String line;
                        while ((line = br.readLine()) != null) {
                            errorBuilder.append(line);
                        }
                        String errorResponse = errorBuilder.toString().trim();
                        Log.e(TAG, "HTTPS错误响应: " + errorResponse);
                        throw new IOException("HTTPS请求失败，响应码: " + responseCode + ", 错误: " + errorResponse);
                    } catch (Exception e) {
                        throw new IOException("HTTPS请求失败，响应码: " + responseCode);
                    }
                }
            } catch (IOException e) {
                Log.e(TAG, "HTTPS数据发送失败: " + e.getMessage());
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRY_COUNT) {
                    try {
                        Log.d(TAG, String.format("HTTPS发送失败，等待%d秒后重试", RETRY_INTERVAL / 1000));
                        Thread.sleep(RETRY_INTERVAL);
                        continue;
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("HTTPS发送被中断", ie);
                    }
                }
                throw e;
            }
        }

        throw new IOException("HTTPS发送失败，已重试" + MAX_RETRY_COUNT + "次: " +
                (lastException != null ? lastException.getMessage() : "未知错误"));
    }

    /**
     * 构建扫描数据
     * 
     * @param scanResult 原始扫描结果
     * @param type       扫描类型（scan/rfid）
     * @return 格式化后的JSON数据
     */
    private String buildScanData(String scanResult, String type) {
        try {
            // 创建JSON对象
            JSONObject jsonObject = new JSONObject();

            // 添加基本信息
            jsonObject.put("type", type.toUpperCase());

            // 处理扫描结果数据
            String processedData;
            JSONArray tagArray = getJsonArray(scanResult, type);
            jsonObject.put("data", tagArray);

            // 添加时间戳
            jsonObject.put("timestamp", System.currentTimeMillis());

            // 添加设备信息
            jsonObject.put("deviceId", getDeviceId());
            jsonObject.put("deviceName", getDeviceName());

            // // 添加扫描信息
            // JSONObject scanInfo = new JSONObject();
            // scanInfo.put("scanTime",
            // new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new
            // Date()));
            // scanInfo.put("scanType", type);
            // jsonObject.put("scanInfo", scanInfo);
            //
            // // 添加版本信息
            // jsonObject.put("version", "1.0");

            // 记录构建的JSON数据
            String jsonString = jsonObject.toString();
            Log.d(TAG, "构建的JSON数据: " + jsonString);

            return jsonString;
        } catch (Exception e) {
            Log.e(TAG, "构建扫描数据时发生错误: " + e.getMessage());
            // 如果JSON构建失败，返回基本格式
            return String.format("{\"type\":\"%s\",\"data\":\"%s\",\"timestamp\":%d,\"version\":\"1.0\"}",
                    type.toUpperCase(),
                    scanResult.replace("\"", "\\\""), // 转义双引号
                    System.currentTimeMillis());
        }
    }

    @NotNull
    private JSONArray getJsonArray(String scanResult, String type) {
        JSONArray tagArray = new JSONArray();
        if (type.toUpperCase().equals("RFID")) {
            // 对于RFID数据，将多行数据转换为数组
            String[] tags = scanResult.split("\n");
            String strData = "";
            for (String tag : tags) {
                if (tag != null && !tag.trim().isEmpty()) {
                    tagArray.put(tag.trim());
                    strData = strData == "" ? tag.trim() : tag.trim() + "," + strData;
                }
            }
            // jsonObject.put("data", tagArray);
        } else {
            // 对于普通扫描数据，直接使用
            tagArray.put(scanResult.trim());
        }
        return tagArray;
    }

    /**
     * 获取设备ID
     */
    private String getDeviceId() {
        return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
    }

    /**
     * 获取设备名称
     */
    private String getDeviceName() {
        return Build.MANUFACTURER + " " + Build.MODEL;
    }

    /**
     * 调试方法：打印当前连接状态和配置信息
     */
    public void debugConnectionInfo() {
        Log.d(TAG, "=== 连接调试信息 ===");
        Log.d(TAG, "服务器地址: " + serverAddress);
        Log.d(TAG, "端口: " + port);
        Log.d(TAG, "连接类型: " + connectionType + " (0:TCP, 1:UDP, 2:HTTP, 3:HTTPS)");
        Log.d(TAG, "重试次数: " + retryCount);
        Log.d(TAG, "连接状态: " + (isConnected ? "已连接" : "未连接"));
        Log.d(TAG, "TCP Socket状态: " + (tcpSocket != null ? (tcpSocket.isClosed() ? "已关闭" : "已打开") : "未创建"));
        Log.d(TAG, "读取超时: " + READ_TIMEOUT + "ms");
        Log.d(TAG, "响应超时倍数: " + RESPONSE_TIMEOUT_MULTIPLIER);
        Log.d(TAG, "实际响应超时: " + (READ_TIMEOUT * RESPONSE_TIMEOUT_MULTIPLIER) + "ms");
        Log.d(TAG, "重试间隔: " + RETRY_INTERVAL + "ms");
        Log.d(TAG, "最大重试次数: " + MAX_RETRY_COUNT);
        Log.d(TAG, "等待响应: " + (WAIT_FOR_RESPONSE ? "是" : "否"));
        Log.d(TAG, "初始读取超时: " + INITIAL_READ_TIMEOUT + "ms");
        Log.d(TAG, "二次读取超时: " + SECONDARY_READ_TIMEOUT + "ms");
        Log.d(TAG, "==================");
    }

    /**
     * 详细的TCP连接调试方法
     */
    public void debugTCPConnection() {
        Log.d(TAG, "=== TCP连接详细调试 ===");

        if (tcpSocket == null) {
            Log.e(TAG, "TCP Socket为null");
            return;
        }

        try {
            Log.d(TAG, "Socket地址: " + tcpSocket.getInetAddress());
            Log.d(TAG, "本地端口: " + tcpSocket.getLocalPort());
            Log.d(TAG, "远程端口: " + tcpSocket.getPort());
            Log.d(TAG, "连接状态: " + (tcpSocket.isConnected() ? "已连接" : "未连接"));
            Log.d(TAG, "关闭状态: " + (tcpSocket.isClosed() ? "已关闭" : "未关闭"));
            Log.d(TAG, "绑定状态: " + (tcpSocket.isBound() ? "已绑定" : "未绑定"));

            // 检查输入流
            try {
                java.io.InputStream inputStream = tcpSocket.getInputStream();
                int available = inputStream.available();
                Log.d(TAG, "输入流可用字节数: " + available);
            } catch (Exception e) {
                Log.e(TAG, "检查输入流失败: " + e.getMessage());
            }

            // 检查输出流
            try {
                java.io.OutputStream outputStream = tcpSocket.getOutputStream();
                Log.d(TAG, "输出流状态: " + (outputStream != null ? "正常" : "异常"));
            } catch (Exception e) {
                Log.e(TAG, "检查输出流失败: " + e.getMessage());
            }

        } catch (Exception e) {
            Log.e(TAG, "TCP连接调试失败: " + e.getMessage());
        }

        Log.d(TAG, "========================");
    }

    /**
     * 测试TCP连接并发送测试数据
     */
    public String testTCPConnection() {
        Log.d(TAG, "=== 开始TCP连接测试 ===");

        if (!isConnected) {
            Log.e(TAG, "未连接到服务器");
            return "未连接到服务器";
        }

        if (connectionType != 0) {
            Log.e(TAG, "当前不是TCP连接类型");
            return "当前不是TCP连接类型";
        }

        debugTCPConnection();

        try {
            // 发送简单的测试数据
            String testData = "{\"type\":\"TEST\",\"data\":\"ping\",\"timestamp\":" + System.currentTimeMillis() + "}";
            Log.d(TAG, "发送测试数据: " + testData);

            String result = sendTCPDataWithResponse(testData);
            Log.d(TAG, "测试结果: " + result);

            return result;
        } catch (Exception e) {
            Log.e(TAG, "TCP连接测试失败: " + e.getMessage());
            e.printStackTrace();
            return "测试失败: " + e.getMessage();
        }
    }

    /**
     * 简单的TCP响应测试方法
     * 用于快速验证TCP响应读取功能
     */
    public String testTCPResponse() {
        Log.d(TAG, "=== 开始TCP响应测试 ===");

        // 确保连接状态
        if (!ensureConnection()) {
            // result[0] = "无法建立服务器连接";
            // latch.countDown();
            Log.d(TAG, "无法建立服务器连接");
            return "无法建立服务器连接";
        }
        // if (!isConnected || connectionType != 0) {
        // loadSettings();
        // connect();
        // // return "TCP连接不可用";
        // }
        //
        // debugTCPConnection();

        try {
            // 发送测试数据并等待响应
            String testData = "{\"type\":\"PING\",\"data\":\"test\",\"timestamp\":" + System.currentTimeMillis() + "}";
            Log.d(TAG, "发送PING测试数据");

            // 使用快速发送模式
            String quickResult = sendDataQuick(testData);
            Log.d(TAG, "快速发送结果: " + quickResult);

            // 使用响应等待模式
            String responseResult = sendTCPDataWithResponse(testData);
            Log.d(TAG, "响应等待结果: " + responseResult);

            return "快速发送: " + quickResult + ", 响应等待: " + responseResult;

        } catch (Exception e) {
            Log.e(TAG, "TCP响应测试失败: " + e.getMessage());
            return "测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试增强版TCP响应读取
     */
    public String testEnhancedTCPResponse() {
        Log.d(TAG, "=== 开始增强版TCP响应测试 ===");

        // 确保连接状态
        if (!ensureConnection()) {
            Log.d(TAG, "无法建立服务器连接");
            return "无法建立服务器连接";
        }

        try {
            // 发送测试数据并使用增强版响应读取
            String testData = "{\"type\":\"ENHANCED_TEST\",\"data\":\"test_enhanced\",\"timestamp\":"
                    + System.currentTimeMillis() + "}";
            Log.d(TAG, "发送增强版测试数据");

            // 使用增强版响应读取方法
            String result = sendTCPDataWithResponse(testData, true);
            Log.d(TAG, "增强版响应读取结果: " + result);

            return "增强版测试结果: " + result;

        } catch (Exception e) {
            Log.e(TAG, "增强版TCP响应测试失败: " + e.getMessage());
            return "增强版测试失败: " + e.getMessage();
        }
    }

    /**
     * 设置是否等待响应
     * 
     * @param waitForResponse true表示等待响应，false表示不等待
     */
    public void setWaitForResponse(boolean waitForResponse) {
        // 注意：这是一个静态配置，需要在类级别修改
        Log.d(TAG, "响应等待设置: " + (waitForResponse ? "等待响应" : "不等待响应"));
        // 由于Java的限制，这里只能记录日志，实际修改需要在代码中手动调整WAIT_FOR_RESPONSE常量
    }

    /**
     * 获取当前响应等待配置
     */
    public boolean isWaitForResponse() {
        return WAIT_FOR_RESPONSE;
    }

    /**
     * 发送数据但不等待响应（快速发送模式）
     */
    public String sendDataQuick(String data) {
        if (!isConnected) {
            return "未连接到服务器";
        }

        try {
            switch (connectionType) {
                case 0: // TCP
                    if (tcpSocket != null && !tcpSocket.isClosed()) {
                        // 只发送数据，不等待响应
                        java.io.OutputStream out = tcpSocket.getOutputStream();
                        String lengthPrefix = String.format("%04d", data.length());
                        out.write(lengthPrefix.getBytes());
                        out.write(data.getBytes());
                        out.write("\r\n".getBytes());
                        out.flush();
                        Log.d(TAG, "快速发送TCP数据完成");
                        return "success";
                    } else {
                        return "TCP连接未建立";
                    }
                case 1: // UDP
                    sendUDPData(data);
                    return "success";
                case 2: // HTTP
                    return sendHTTPDataWithResponse(data);
                case 3: // HTTPS
                    return sendHTTPSDataWithResponse(data);
                default:
                    return "未知连接类型";
            }
        } catch (Exception e) {
            Log.e(TAG, "快速发送失败: " + e.getMessage());
            return "发送失败: " + e.getMessage();
        }
    }

    /**
     * TCP响应读取问题诊断指南
     * 
     * 如果遇到无法读取服务响应的问题，请按以下步骤进行诊断：
     * 
     * 1. 检查连接状态：
     * mesCommunication.debugConnectionInfo();
     * mesCommunication.debugTCPConnection();
     * 
     * 2. 测试TCP连接：
     * String result = mesCommunication.testTCPConnection();
     * Log.d("TEST", "TCP测试结果: " + result);
     * 
     * 3. 测试增强版响应读取（推荐）：
     * String enhancedResult = mesCommunication.testEnhancedTCPResponse();
     * Log.d("TEST", "增强版测试结果: " + enhancedResult);
     * 
     * 4. 检查服务器配置：
     * - 确认服务器地址和端口正确
     * - 确认服务器支持TCP连接
     * - 确认服务器会发送响应
     * 
     * 5. 常见问题及解决方案：
     * 
     * 问题1：服务器不发送响应
     * 解决：设置 WAIT_FOR_RESPONSE = false 或使用 sendDataQuick()
     * 
     * 问题2：响应格式不匹配
     * 解决：检查服务器响应格式，调整响应解析逻辑
     * 
     * 问题3：超时时间过短
     * 解决：增加 INITIAL_READ_TIMEOUT 和 SECONDARY_READ_TIMEOUT 的值
     * 
     * 问题4：Socket连接问题
     * 解决：重新建立连接或检查网络状态
     * 
     * 问题5：有数据可读但读取超时（新增）
     * 症状：日志显示"检测到输入流有数据可读"但随后"读取响应超时或异常"
     * 原因：
     * - 服务器发送的数据格式与客户端期望不匹配
     * - 数据不包含换行符，但客户端使用readLine()读取
     * - 字符编码问题
     * - 数据被分片发送
     * 解决：
     * - 使用增强版响应读取方法：testEnhancedTCPResponse()
     * - 检查服务器发送的数据格式
     * - 调整数据读取策略
     * 
     * 6. 调试日志分析：
     * - 查看 "=== 开始TCP响应读取 ===" 日志
     * - 检查 "检测到输入流有数据可读" 日志
     * - 分析 "读取到原始响应数据" 日志
     * - 查看 "读取响应超时或异常" 日志
     * 
     * 7. 使用建议：
     * - 对于不需要响应的场景，使用 sendDataQuick()
     * - 对于需要响应的场景，优先使用增强版响应读取方法
     * - 定期调用 debugTCPConnection() 检查连接状态
     * - 如果标准方法失败，尝试增强版方法
     * 
     * 8. 增强版响应读取的优势：
     * - 支持多种数据格式（JSON、纯文本、二进制等）
     * - 更短的超时时间，避免长时间阻塞
     * - 多次尝试读取，提高成功率
     * - 智能判断响应完整性
     */
}