package com.zebra.demo.mes_settings;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import androidx.fragment.app.Fragment;
import com.zebra.demo.ActiveDeviceActivity;
import com.zebra.demo.R;
import com.zebra.demo.rfidreader.manager.ManagerFragment;
import com.zebra.demo.rfidreader.settings.BackPressedFragment;
import com.zebra.demo.scanner.helpers.ActiveDeviceAdapter;

public class MESSettingsFragment extends Fragment {
    private EditText serverAddressEditText;
    private EditText portEditText;
    private EditText retryCountEditText;
    private Spinner connectionTypeSpinner;
    private Button saveButton;
    private SharedPreferences sharedPreferences;

    public static MESSettingsFragment newInstance() {
        return new MESSettingsFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_mes_settings, container, false);

        // Initialize views
        serverAddressEditText = view.findViewById(R.id.serverAddress);
        portEditText = view.findViewById(R.id.portNumber);
        retryCountEditText = view.findViewById(R.id.retryCount);
        connectionTypeSpinner = view.findViewById(R.id.connectionType);
        saveButton = view.findViewById(R.id.saveButton);

        // Initialize SharedPreferences
        sharedPreferences = requireActivity().getSharedPreferences("MESSettings", 0);

        // Setup connection type spinner
        setupConnectionTypeSpinner();

        // Load saved settings
        loadSettings();

        // Set up save button click listener
        saveButton.setOnClickListener(v -> saveSettings());

        return view;
    }

    // @Override
    // public void onBackPressed() {
    // if (getActivity() instanceof ActiveDeviceActivity) {
    // ((ActiveDeviceActivity)
    // getActivity()).loadNextFragment(ActiveDeviceAdapter.MAIN_HOME_SETTINGS_TAB);
    // }
    // }

    private void setupConnectionTypeSpinner() {
        String[] connectionTypes = { "TCP", "UDP", "HTTP", "HTTPS" };
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
                requireContext(),
                android.R.layout.simple_spinner_item,
                connectionTypes);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        connectionTypeSpinner.setAdapter(adapter);
    }

    private void loadSettings() {
        serverAddressEditText.setText(sharedPreferences.getString("server_address", ""));
        portEditText.setText(sharedPreferences.getString("port", ""));
        retryCountEditText.setText(String.valueOf(sharedPreferences.getInt("retry_count", 3)));
        int connectionType = sharedPreferences.getInt("connection_type", 0);
        connectionTypeSpinner.setSelection(connectionType);
    }

    private void saveSettings() {
        String serverAddress = serverAddressEditText.getText().toString().trim();
        String port = portEditText.getText().toString().trim();
        String retryCount = retryCountEditText.getText().toString().trim();

        if (serverAddress.isEmpty() || port.isEmpty() || retryCount.isEmpty()) {
            Toast.makeText(getContext(), R.string.please_fill_all_required_fields, Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            int portNumber = Integer.parseInt(port);
            int retryCountNumber = Integer.parseInt(retryCount);

            if (portNumber <= 0 || portNumber > 65535) {
                Toast.makeText(getContext(), "端口号必须在1-65535之间", Toast.LENGTH_SHORT).show();
                return;
            }

            if (retryCountNumber < 0 || retryCountNumber > 10) {
                Toast.makeText(getContext(), "重试次数必须在0-10之间", Toast.LENGTH_SHORT).show();
                return;
            }

            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString("server_address", serverAddress);
            editor.putString("port", port);
            editor.putInt("retry_count", retryCountNumber);
            editor.putInt("connection_type", connectionTypeSpinner.getSelectedItemPosition());
            editor.apply();

            Toast.makeText(getContext(), "设置已保存", Toast.LENGTH_SHORT).show();
            if (getActivity() instanceof ActiveDeviceActivity) {
                ((ActiveDeviceActivity) getActivity()).loadNextFragment(ActiveDeviceAdapter.MES_SETTINGS_TAB);
            }
        } catch (NumberFormatException e) {
            Toast.makeText(getContext(), "端口号和重试次数必须是有效的数字", Toast.LENGTH_SHORT).show();
        }
    }
}